'use client'

import React, { useEffect, useState } from 'react'
import { useLazyQuery, useMutation } from '@apollo/client'
import UploadCloudFlareCaptures, {
    getCloudFlareImagesFile,
    handleDeleteCFCaptureImage,
} from './upload-images'
import { GET_SECTION_MEMBER_IMAGES } from '@/app/lib/graphQL/query'
import { Button } from '@/components/ui/button'
import { Camera } from 'lucide-react'
import { cn } from '../../../../../utils/cn'
import { toast } from 'sonner'
import { DELETE_SECTION_MEMBER_IMAGE } from '@/app/lib/graphQL/mutation'
import Lightbox from 'yet-another-react-lightbox'
import Captions from 'yet-another-react-lightbox/plugins/captions'
import Zoom from 'yet-another-react-lightbox/plugins/zoom'
import Download from 'yet-another-react-lightbox/plugins/download'
import Fullscreen from 'yet-another-react-lightbox/plugins/fullscreen'
import 'yet-another-react-lightbox/styles.css'
import 'yet-another-react-lightbox/plugins/captions.css'

export default function CloudFlareCaptures({
    inputId = 0,
    buttonType = 'icon',
    sectionName = 'logBookEntryID',
    sectionId = 0,
    editable = true,
}: {
    inputId: number
    buttonType?: string
    sectionName?: string
    sectionId: number
    editable?: boolean
}) {
    const [fieldImages, setFieldImages] = useState<any>([])
    const [allImages, setAllImages] = useState<any>([])
    const [openLightbox, setOpenLightbox] = useState(false)
    const [lightboxImageData, setLightboxImageData] = useState('')
    const [getFieldImages] = useLazyQuery(GET_SECTION_MEMBER_IMAGES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readCaptureImages.nodes
            if (data) {
                setFieldImages(data)
            }
        },
        onError: (error: any) => {
            console.error('getFieldImages error', error)
        },
    })

    const getFile = () => {
        if (fieldImages && Array.isArray(fieldImages)) {
            return fieldImages
                .filter((image: any) => image.fieldName == inputId)
                .sort((a: any, b: any) => b.id - a.id)
        }
        return false
    }

    const refreshImages = async () => {
        await getFieldImages({
            variables: {
                filter: {
                    [sectionName]: { eq: sectionId },
                },
            },
        })
    }

    useEffect(() => {
        // Add a small delay before processing images
        const wait = (ms: number) =>
            new Promise((resolve) => setTimeout(resolve, ms))
        if (fieldImages && fieldImages.length > 0) {
            const loadImages = async () => {
                await wait(1000) // Small delay to ensure state is updated
                try {
                    const imagePromises = fieldImages.map(async (img: any) => {
                        const imageData = await getCloudFlareImagesFile(img)
                        return {
                            ...img,
                            imageData,
                        }
                    })
                    const images = await Promise.all(imagePromises)
                    setAllImages(images)
                } catch (error) {
                    console.error('Error loading images:', error)
                }
            }
            loadImages()
        } else {
            setAllImages([])
        }
    }, [fieldImages])

    useEffect(() => {
        getFieldImages({
            variables: {
                filter: {
                    [sectionName]: { eq: sectionId },
                },
            },
        })
    }, [])

    const type = buttonType === 'icon' ? 'icon' : 'default'

    const [deleteSectionMemberImage] = useMutation(
        DELETE_SECTION_MEMBER_IMAGE,
        {
            onCompleted: (response) => {
                const data = response.deleteCaptureImage
                if (data) {
                    toast.dismiss()
                    toast.success('Image deleted successfully.')
                }
            },
            onError: (error) => {
                console.error('Error deleting image', error)
                toast.error('Failed to delete image.')
            },
        },
    )

    const getFileUrl = () => {
        if (fieldImages && fieldImages.length > 0) {
            const image = fieldImages.find((img: any) => img.id === inputId)
            if (image) {
                return getCloudFlareImagesFile(image)
            }
        }
        return ''
    }

    return (
        <>
            {inputId === 0 || sectionId === 0 ? (
                <div className="w-full flex">
                    <Button
                        variant={buttonType === 'icon' ? 'ghost' : 'outline'}
                        size={type}
                        iconOnly={buttonType === 'icon'}
                        title="Add comment"
                        className={buttonType === 'icon' ? 'group' : 'h-10'}
                        iconLeft={
                            <Camera
                                className={
                                    buttonType === 'icon'
                                        ? cn(
                                              'text-curious-blue-400 group-hover:text-curious-blue-400/50',
                                              'will-change-transform will-change-width will-change-padding transform-gpu',
                                              'group-hover:transition-colors group-hover:ease-out group-hover:duration-300',
                                          )
                                        : ''
                                }
                                size={24}
                            />
                        }
                        onClick={() =>
                            toast.error('Please save the section first', {
                                description:
                                    'You need to save the section in order to capture or upload images.',
                            })
                        }>
                        {buttonType === 'button' && 'Capture / Upload Image'}
                    </Button>
                </div>
            ) : (
                <>
                    {allImages.length > 0 && (
                        <div className="flex flex-wrap mb-4">
                            {allImages.map((img: any, index: number) => (
                                <div
                                    className="w-1/5 p-1 rounded-md relative"
                                    key={index}>
                                    <img
                                        key={index}
                                        src={img.imageData}
                                        alt={`Captured ${index}`}
                                        className="object-cover rounded-md"
                                        onClick={() => {
                                            setLightboxImageData(img.imageData)
                                            setOpenLightbox(true)
                                        }}
                                    />
                                    <Button
                                        variant="destructive"
                                        size="icon"
                                        className="absolute top-1 right-1 p-0 size-5"
                                        onClick={async () => {
                                            const updatedImages =
                                                await handleDeleteCFCaptureImage(
                                                    img,
                                                    allImages,
                                                )
                                            deleteSectionMemberImage({
                                                variables: {
                                                    ids: [+img.id],
                                                },
                                            })
                                            setAllImages(updatedImages)
                                        }}>
                                        &times;
                                    </Button>
                                </div>
                            ))}
                            <Lightbox
                                open={openLightbox}
                                close={() => setOpenLightbox(false)}
                                slides={[
                                    {
                                        src: lightboxImageData,
                                        alt: document.title,
                                        description: document.title,
                                    },
                                ]}
                                render={{
                                    buttonPrev: () => null,
                                    buttonNext: () => null,
                                }}
                                controller={{
                                    closeOnPullUp: true,
                                    closeOnPullDown: true,
                                    closeOnBackdropClick: true,
                                }}
                                plugins={[Captions]}
                            />
                        </div>
                    )}
                    {editable && (
                        <div className="w-full flex">
                            <UploadCloudFlareCaptures
                                file={getFile()}
                                setFile={refreshImages}
                                inputId={inputId.toString()}
                                buttonType={'button'}
                                sectionData={{
                                    id: sectionId,
                                    sectionName: sectionName,
                                }}
                            />
                        </div>
                    )}
                </>
            )}
        </>
    )
}
