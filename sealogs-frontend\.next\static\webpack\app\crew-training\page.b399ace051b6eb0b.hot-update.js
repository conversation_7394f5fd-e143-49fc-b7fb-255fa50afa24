"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/crew-training-client.tsx":
/*!***********************************************************!*\
  !*** ./src/app/ui/crew-training/crew-training-client.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_filter_components_training_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/filter/components/training-actions */ \"(app-pages-browser)/./src/components/filter/components/training-actions.tsx\");\n/* harmony import */ var _app_lib_icons_SealogsTrainingIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/icons/SealogsTrainingIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsTrainingIcon.ts\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_987e738c28daf1a1a086933c5a55be9a/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useUnifiedTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingClient = ()=>{\n    _s();\n    const applyFilterRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    /** ⬅️ 1) reactive state that drives the heading */ const [isOverdueEnabled, setIsOverdueEnabled] = (0,nuqs__WEBPACK_IMPORTED_MODULE_10__.useQueryState)(\"overdue\", nuqs__WEBPACK_IMPORTED_MODULE_10__.parseAsBoolean.withDefault(false));\n    // Training data state\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [includeCompleted, setIncludeCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // GraphQL queries\n    const [loadTrainingSessionDues] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__.READ_TRAINING_SESSION_DUES, {\n        onCompleted: (data)=>{\n            var _data_readTrainingSessionDues;\n            setTrainingSessionDues(((_data_readTrainingSessionDues = data.readTrainingSessionDues) === null || _data_readTrainingSessionDues === void 0 ? void 0 : _data_readTrainingSessionDues.nodes) || []);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    const [loadTrainingList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__.TRAINING_SESSIONS, {\n        onCompleted: (data)=>{\n            var _data_readTrainingSessions;\n            setCompletedTrainingList(((_data_readTrainingSessions = data.readTrainingSessions) === null || _data_readTrainingSessions === void 0 ? void 0 : _data_readTrainingSessions.nodes) || []);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training sessions:\", error);\n        }\n    });\n    // Vessel icon helper\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__.useVesselIconData)();\n    // Memoize the unified data calculation\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_8__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Use unified training filters hook with client-side filtering\n    const { handleFilterChange, filteredData } = (0,_hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters)({\n        initialFilter: {},\n        unifiedData\n    });\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadData = async ()=>{\n            setIsLoading(true);\n            try {\n                await Promise.all([\n                    loadTrainingSessionDues({}),\n                    loadTrainingList(0, {})\n                ]);\n            } catch (error) {\n                console.error(\"Error loading training data:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        loadData();\n    }, [\n        loadTrainingSessionDues,\n        loadTrainingList\n    ]);\n    /** ⬅️ 2) keep both the list and the heading in sync */ const handleDropdownChange = (type, data)=>{\n        var _applyFilterRef_current;\n        if (type === \"overdue\") setIsOverdueEnabled(!!data);\n        (_applyFilterRef_current = applyFilterRef.current) === null || _applyFilterRef_current === void 0 ? void 0 : _applyFilterRef_current.apply({\n            type,\n            data\n        });\n    };\n    // Handle filter changes from the FilterTable toolbar\n    const handleFilterTableChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((filterData)=>{\n        handleFilterChange(filterData);\n    }, [\n        handleFilterChange\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_4__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsTrainingIcon__WEBPACK_IMPORTED_MODULE_3__.SealogsTrainingIcon, {\n                    className: \"size-12\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 23\n                }, void 0),\n                title: \"Crew Trainings\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_training_actions__WEBPACK_IMPORTED_MODULE_2__.CrewTrainingFilterActions, {\n                    onChange: (data)=>{\n                        handleDropdownChange(\"overdue\", data);\n                    },\n                    overdueList: isOverdueEnabled\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                lineNumber: 117,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_5__.UnifiedTrainingTable, {\n                    unifiedData: filteredData,\n                    getVesselWithIcon: getVesselWithIcon,\n                    includeCompleted: includeCompleted,\n                    isVesselView: false,\n                    showToolbar: true,\n                    isLoading: isLoading,\n                    pageSize: 20,\n                    onChange: handleFilterTableChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                lineNumber: 129,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CrewTrainingClient, \"tC0kg3JeEWQeqXJMohZdWoL6//w=\", false, function() {\n    return [\n        nuqs__WEBPACK_IMPORTED_MODULE_10__.useQueryState,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__.useVesselIconData,\n        _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters\n    ];\n});\n_c = CrewTrainingClient;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingClient);\nvar _c;\n$RefreshReg$(_c, \"CrewTrainingClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/crew-training-client.tsx\n"));

/***/ })

});