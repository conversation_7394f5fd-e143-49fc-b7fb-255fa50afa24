"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/crew-training-client.tsx":
/*!***********************************************************!*\
  !*** ./src/app/ui/crew-training/crew-training-client.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_filter_components_training_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/filter/components/training-actions */ \"(app-pages-browser)/./src/components/filter/components/training-actions.tsx\");\n/* harmony import */ var _app_lib_icons_SealogsTrainingIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/icons/SealogsTrainingIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsTrainingIcon.ts\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_987e738c28daf1a1a086933c5a55be9a/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useUnifiedTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingClient = ()=>{\n    _s();\n    const applyFilterRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    /** ⬅️ 1) reactive state that drives the heading */ const [isOverdueEnabled, setIsOverdueEnabled] = (0,nuqs__WEBPACK_IMPORTED_MODULE_13__.useQueryState)(\"overdue\", nuqs__WEBPACK_IMPORTED_MODULE_13__.parseAsBoolean.withDefault(false));\n    // Training data state\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [includeCompleted, setIncludeCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // GraphQL queries\n    const [loadTrainingSessionDues] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes;\n            if (data) {\n                // Filter out crew members who are no longer assigned to the vessel (same as list.tsx)\n                const filteredData = data.filter((item)=>item.vessel.seaLogsMembers.nodes.some((m)=>{\n                        return m.id === item.memberID;\n                    }));\n                const dueWithStatus = filteredData.map((due)=>{\n                    return {\n                        ...due,\n                        status: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_12__.GetTrainingSessionStatus)(due)\n                    };\n                });\n                // Group dues by vessel, training type, and due date (same logic as list.tsx)\n                const groupedDues = dueWithStatus.reduce((acc, due)=>{\n                    const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n                    if (!acc[key]) {\n                        var _due_trainingSession;\n                        acc[key] = {\n                            id: due.id,\n                            vesselID: due.vesselID,\n                            vessel: due.vessel,\n                            trainingTypeID: due.trainingTypeID,\n                            trainingType: due.trainingType,\n                            dueDate: due.dueDate,\n                            status: due.status,\n                            trainingLocationType: (_due_trainingSession = due.trainingSession) === null || _due_trainingSession === void 0 ? void 0 : _due_trainingSession.trainingLocationType,\n                            members: []\n                        };\n                    }\n                    acc[key].members.push(due.member);\n                    return acc;\n                }, {});\n                const mergedDues = Object.values(groupedDues).map((group)=>{\n                    const mergedMembers = group.members.reduce((acc, member)=>{\n                        const existingMember = acc.find((m)=>m.id === member.id);\n                        if (existingMember) {\n                            existingMember.firstName = member.firstName;\n                            existingMember.surname = member.surname;\n                        } else {\n                            acc.push(member);\n                        }\n                        return acc;\n                    }, []);\n                    return {\n                        id: group.id,\n                        vesselID: group.vesselID,\n                        vessel: group.vessel,\n                        trainingTypeID: group.trainingTypeID,\n                        trainingType: group.trainingType,\n                        status: group.status,\n                        dueDate: group.dueDate,\n                        trainingLocationType: group.trainingLocationType,\n                        members: mergedMembers\n                    };\n                });\n                setTrainingSessionDues(mergedDues);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    const [loadTrainingList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes;\n            // Transform vessel data to include complete vessel information with position (same as list.tsx)\n            const transformedData = data.map((item)=>{\n                const completeVesselData = getVesselWithIcon(item.vessel.id, item.vessel);\n                return {\n                    ...item,\n                    vessel: completeVesselData\n                };\n            });\n            if (transformedData) {\n                setCompletedTrainingList(transformedData);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training sessions:\", error);\n        }\n    });\n    // Vessel icon helper\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__.useVesselIconData)();\n    // Memoize the unified data calculation\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_8__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Use unified training filters hook with client-side filtering\n    const { handleFilterChange, filteredData } = (0,_hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters)({\n        initialFilter: {},\n        unifiedData\n    });\n    // Initialize permissions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.getPermissions);\n    }, []);\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadData = async ()=>{\n            setIsLoading(true);\n            try {\n                await Promise.all([\n                    loadTrainingSessionDues({\n                        variables: {\n                            filter: {}\n                        }\n                    }),\n                    loadTrainingList({\n                        variables: {\n                            filter: {},\n                            offset: 0,\n                            limit: 100\n                        }\n                    })\n                ]);\n            } catch (error) {\n                console.error(\"Error loading training data:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        loadData();\n    }, [\n        loadTrainingSessionDues,\n        loadTrainingList\n    ]);\n    /** ⬅️ 2) keep both the list and the heading in sync */ const handleDropdownChange = (type, data)=>{\n        var _applyFilterRef_current;\n        if (type === \"overdue\") setIsOverdueEnabled(!!data);\n        (_applyFilterRef_current = applyFilterRef.current) === null || _applyFilterRef_current === void 0 ? void 0 : _applyFilterRef_current.apply({\n            type,\n            data\n        });\n    };\n    // Handle filter changes from the FilterTable toolbar\n    const handleFilterTableChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((filterData)=>{\n        handleFilterChange(filterData);\n    }, [\n        handleFilterChange\n    ]);\n    // Permission guards - same as in list.tsx\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n            lineNumber: 225,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n            lineNumber: 227,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_4__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsTrainingIcon__WEBPACK_IMPORTED_MODULE_3__.SealogsTrainingIcon, {\n                    className: \"size-12\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 23\n                }, void 0),\n                title: \"Crew Trainings\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_training_actions__WEBPACK_IMPORTED_MODULE_2__.CrewTrainingFilterActions, {\n                    onChange: (data)=>{\n                        handleDropdownChange(\"overdue\", data);\n                    },\n                    overdueList: isOverdueEnabled\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                lineNumber: 233,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_5__.UnifiedTrainingTable, {\n                    unifiedData: filteredData,\n                    getVesselWithIcon: getVesselWithIcon,\n                    includeCompleted: includeCompleted,\n                    isVesselView: false,\n                    showToolbar: true,\n                    isLoading: isLoading,\n                    pageSize: 20,\n                    onChange: handleFilterTableChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                lineNumber: 245,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CrewTrainingClient, \"8eW10xk4JjGpvhU7hxsExxQ2PpU=\", false, function() {\n    return [\n        nuqs__WEBPACK_IMPORTED_MODULE_13__.useQueryState,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__.useVesselIconData,\n        _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters\n    ];\n});\n_c = CrewTrainingClient;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingClient);\nvar _c;\n$RefreshReg$(_c, \"CrewTrainingClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/crew-training-client.tsx\n"));

/***/ })

});