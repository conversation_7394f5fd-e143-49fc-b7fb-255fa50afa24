"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/maintenance/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/components/upload-images.tsx":
/*!*********************************************************!*\
  !*** ./src/app/ui/logbook/components/upload-images.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UploadCloudFlareCaptures; },\n/* harmony export */   getCloudFlareImagesFile: function() { return /* binding */ getCloudFlareImagesFile; },\n/* harmony export */   handleDeleteCFCaptureImage: function() { return /* binding */ handleDeleteCFCaptureImage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! aws-sdk */ \"(app-pages-browser)/./node_modules/.pnpm/aws-sdk@2.1692.0/node_modules/aws-sdk/lib/browser.js\");\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(aws_sdk__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Camera_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Camera!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../../utils/cn */ \"(app-pages-browser)/./utils/cn.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _upload_cf__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./upload-cf */ \"(app-pages-browser)/./src/app/ui/logbook/components/upload-cf.tsx\");\n/* harmony import */ var _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../../../utils/responsiveLabel */ \"(app-pages-browser)/./utils/responsiveLabel.ts\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/.pnpm/buffer@4.9.2/node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ default,getCloudFlareImagesFile,handleDeleteCFCaptureImage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ACCOUNT_ID = \"ddde1c1cd1aa25641691808dcbafdeb7\";\nconst ACCESS_KEY_ID = \"06c3e13a539f24e6fdf7075bf381bf5e\";\nconst SECRET_ACCESS_KEY = \"0bc23db8559504fb300b54def562d007e4a373fb940a7d07617ce906c553bbe8\";\nconst s3Client = new (aws_sdk__WEBPACK_IMPORTED_MODULE_2___default().S3)({\n    endpoint: \"https://\".concat(ACCOUNT_ID, \".r2.cloudflarestorage.com\"),\n    accessKeyId: ACCESS_KEY_ID,\n    secretAccessKey: SECRET_ACCESS_KEY,\n    signatureVersion: \"v4\",\n    region: \"auto\"\n});\nfunction UploadCloudFlareCaptures(param) {\n    let { file = false, setFile, inputId, buttonType = \"icon\", sectionData = {\n        id: 0,\n        sectionName: \"logBookEntryID\"\n    } } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const [openCameraDialog, setOpenCameraDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [image, setImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayImage, setDisplayImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clientID, setClientID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [devices, setDevices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fileUpload, setFileUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadFileNames, setUploadFileNames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allImages, setAllImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allFiles, setAllFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    const responsiveLabel = (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__.useResponsiveLabel)(\"phablet\");\n    const getFile = (file)=>{\n        if (!file || !file.name) {\n            console.error(\"No file name provided\");\n            return;\n        }\n        s3Client.getObject({\n            Bucket: \"captures\",\n            Key: file.name\n        }, async (err, data)=>{\n            if (err) {\n                console.error(err);\n                if (file.id) {\n                    deleteSectionMemberImage({\n                        variables: {\n                            ids: [\n                                +file.id\n                            ]\n                        }\n                    });\n                }\n            } else {\n                if (!file || !file.name) {\n                    console.error(\"No file name provided\");\n                    return;\n                }\n                const fileType = file.name.split(\".\").pop() || \"\";\n                const blob = new Blob([\n                    new Uint8Array(data === null || data === void 0 ? void 0 : data.Body)\n                ]);\n                const url = URL.createObjectURL(blob);\n                if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {\n                    // setImage(url)\n                    // setDisplayImage(true)\n                    const base64String = Buffer.from(data === null || data === void 0 ? void 0 : data.Body).toString(\"base64\");\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    var base64Image = \"data:image/\".concat(fileType, \";base64,\").concat(base64String);\n                    if (!textContent.startsWith(\"�PNG\")) {\n                        base64Image = textContent;\n                    }\n                    // setImage(base64Image)\n                    if (allImages.find((img)=>img.name === file.name) === undefined) {\n                        setAllImages((prev)=>[\n                                ...prev,\n                                {\n                                    ...file,\n                                    imageData: base64Image\n                                }\n                            ]);\n                    } else {\n                        setAllImages((prev)=>prev.map((img)=>img.name === file.name ? {\n                                    ...img,\n                                    imageData: base64Image\n                                } : img));\n                    }\n                } else {\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    // setImage(textContent)\n                    if (allImages.find((img)=>img.name === file.name) === undefined) {\n                        setAllImages((prev)=>[\n                                ...prev,\n                                {\n                                    ...file,\n                                    imageData: textContent\n                                }\n                            ]);\n                    } else {\n                        setAllImages((prev)=>prev.map((img)=>img.name === file.name ? {\n                                    ...img,\n                                    imageData: textContent\n                                } : img));\n                    }\n                // setDisplayImage(true)\n                }\n            }\n        });\n    };\n    // Handle opening the camera dialog\n    const handleOpenCameraDialog = async function() {\n        let bypass = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        setOpenCameraDialog(true);\n        setUploadFileNames(false);\n        setFileUpload(false);\n        setImage(null);\n        setDisplayImage(false);\n        if (file && file.length > 0 && !bypass) {\n            file.forEach((f)=>{\n                getFile(f);\n            });\n        // return\n        }\n        const devices = await navigator.mediaDevices.enumerateDevices();\n        const hasEnvironmentCamera = devices.some((device)=>device.kind === \"videoinput\");\n        if (hasEnvironmentCamera) {\n            setDevices(devices.filter((device)=>device.kind === \"videoinput\"));\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No camera found. Please connect a camera.\");\n            return;\n        }\n        navigator.mediaDevices.getUserMedia({\n            video: {\n                facingMode: \"environment\"\n            },\n            audio: false\n        }).then((stream)=>{\n            const videoElement = document.getElementById(\"camera-video\");\n            videoElement.srcObject = stream;\n            videoElement.play();\n        }).catch((error)=>{\n            console.error(\"Error accessing camera:\", error);\n        });\n    };\n    const captureImage = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (!videoElement) {\n            console.error(\"Video element not found\");\n            return;\n        }\n        const canvas = document.createElement(\"canvas\");\n        canvas.width = videoElement.videoWidth;\n        canvas.height = videoElement.videoHeight;\n        const context = canvas.getContext(\"2d\");\n        if (!context) {\n            console.error(\"Failed to get canvas context\");\n            return;\n        }\n        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);\n        const imageData = canvas.toDataURL(\"image/png\");\n        // Stop the camera stream after capturing the image\n        if (videoElement.srcObject) {\n            const stream = videoElement.srcObject;\n            const tracks = stream.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoElement.srcObject = null;\n        }\n        if (imageData) {\n            // setImage(imageData)\n            // setDisplayImage(true)\n            setAllImages((prev)=>[\n                    ...prev,\n                    {\n                        name: clientID + \"-capture-\" + Date.now(),\n                        imageData: imageData\n                    }\n                ]);\n            setImage(null);\n            setDisplayImage(false);\n            handleOpenCameraDialog(true);\n        }\n    };\n    const turnOffCamera = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (videoElement && videoElement.srcObject) {\n            const stream = videoElement.srcObject;\n            const tracks = stream.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoElement.srcObject = null;\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _localStorage_getItem;\n        setClientID(+((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0));\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (openCameraDialog) return;\n        turnOffCamera();\n    }, [\n        openCameraDialog\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (allFiles.length > 0) {\n            allFiles.forEach((file)=>{\n                if (file.title) {\n                    getFile({\n                        name: file.title,\n                        fieldName: inputId\n                    });\n                    setUploadFileNames((prev)=>{\n                        if (Array.isArray(prev)) {\n                            return [\n                                ...prev,\n                                file.title\n                            ];\n                        }\n                        return [\n                            file.title\n                        ];\n                    });\n                }\n            });\n        }\n    }, [\n        allFiles\n    ]);\n    const [createSectionMemberImage] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.CREATE_SECTION_MEMBER_IMAGE, {\n        onCompleted: (response)=>{\n            const data = response.createSectionMemberImage;\n            setFile();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating comment\", error);\n        }\n    });\n    async function uploadFile(file) {\n        // Upload file to Cloudflare\n        var fileName;\n        if (file.imageData) {\n            fileName = file.name || clientID + \"-capture-\" + Date.now();\n        }\n        if (!fileName) {\n            fileName = clientID + \"-capture-\" + Date.now();\n        }\n        createSectionMemberImage({\n            variables: {\n                input: {\n                    name: fileName,\n                    fieldName: inputId,\n                    imageType: \"FieldImage\",\n                    [sectionData.sectionName]: sectionData.sectionName === \"logBookEntryID\" ? logentryID : sectionData.id\n                }\n            }\n        });\n        if (file.imageData) {\n            s3Client.putObject({\n                Bucket: \"captures\",\n                Key: fileName,\n                Body: file.imageData\n            }, (err, data)=>{\n                if (err) {\n                    console.error(err);\n                } else {\n                    setFile();\n                }\n            });\n        }\n    }\n    const switchCamera = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (!videoElement) {\n            console.error(\"Video element not found\");\n            return;\n        }\n        const currentDeviceId = videoElement.srcObject ? videoElement.srcObject.getVideoTracks()[0].getSettings().deviceId : null;\n        const nextDevice = devices.find((device)=>device.kind === \"videoinput\" && device.deviceId !== currentDeviceId);\n        if (nextDevice) {\n            navigator.mediaDevices.getUserMedia({\n                video: {\n                    deviceId: nextDevice.deviceId\n                },\n                audio: false\n            }).then((stream)=>{\n                videoElement.srcObject = stream;\n                videoElement.play();\n            }).catch((error)=>{\n                console.error(\"Error switching camera:\", error);\n            });\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No other camera found to switch.\");\n        }\n    };\n    const handleUploadFile = ()=>{\n        if (allImages.length === 0) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please capture or upload an image first.\");\n            return;\n        }\n        allImages.forEach((img)=>{\n            uploadFile(img);\n        });\n        setAllImages([]);\n        setAllFiles([]);\n        setImage(null);\n        setDisplayImage(false);\n        setOpenCameraDialog(false);\n        turnOffCamera();\n        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Images uploaded successfully.\");\n    };\n    const [deleteSectionMemberImage] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.DELETE_SECTION_MEMBER_IMAGE, {\n        onCompleted: (response)=>{\n            const data = response.deleteCaptureImage;\n            if (data) {\n                setFile();\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image deleted successfully.\");\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error deleting image\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete image.\");\n        }\n    });\n    const handleDeleteImage = (img)=>{\n        setAllImages((prev)=>prev.filter((image)=>image.name !== img.name));\n        if (img.imageData) {\n            s3Client.deleteObject({\n                Bucket: \"captures\",\n                Key: img.name || \"\"\n            }, (err, data)=>{\n                if (err) {\n                    console.error(\"Error deleting image:\", err);\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image deleted successfully.\");\n                }\n            });\n            if (!img.id) {\n                return;\n            }\n            deleteSectionMemberImage({\n                variables: {\n                    ids: [\n                        +img.id\n                    ]\n                }\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: buttonType === \"icon\" ? \"ghost\" : \"outline\",\n                iconOnly: buttonType === \"icon\",\n                size: buttonType === \"icon\" ? \"icon\" : \"default\",\n                title: \"Add comment\",\n                className: buttonType === \"icon\" ? \"group\" : \"\",\n                iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: buttonType === \"icon\" ? (0,_utils_cn__WEBPACK_IMPORTED_MODULE_5__.cn)(file && file.length > 0 ? \"text-curious-blue-400 group-hover:text-curious-blue-400/50\" : \"text-outer-space-400 group-hover:text-outer-space-600\", \"will-change-transform will-change-width will-change-padding transform-gpu\", \"group-hover:transition-colors group-hover:ease-out group-hover:duration-300\") : \"\",\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                    lineNumber: 470,\n                    columnNumber: 21\n                }, void 0),\n                onClick: ()=>handleOpenCameraDialog(false),\n                children: buttonType === \"button\" && (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__.getResponsiveLabel)(bp.phablet, \"Capture / Upload\", \"Capture / Upload Image\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                lineNumber: 463,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AlertDialogNew, {\n                openDialog: openCameraDialog,\n                setOpenDialog: setOpenCameraDialog,\n                size: \"lg\",\n                title: fileUpload ? \"Files\" : \"Camera\",\n                handleCreate: ()=>{\n                    // if (image) {\n                    handleUploadFile();\n                // setOpenCameraDialog(false)\n                // } else {\n                // toast.error('Please capture an image first.')\n                // }\n                },\n                handleCancel: ()=>{\n                    setOpenCameraDialog(false);\n                    setImage(null);\n                    setDisplayImage(false);\n                    setAllImages([]);\n                    turnOffCamera();\n                    setAllFiles([]);\n                    setFile();\n                },\n                actionText: \"Save\",\n                cancelText: \"Close\",\n                loading: false,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            allImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap mb-4\",\n                                children: allImages.filter((img)=>!(img === null || img === void 0 ? void 0 : img.id)).map((img, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1/4 p-1 rounded-md relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: img.imageData,\n                                                alt: \"Captured \".concat(index),\n                                                className: \"object-cover\",\n                                                onClick: ()=>{\n                                                    setImage(img.imageData);\n                                                    setDisplayImage(true);\n                                                    turnOffCamera();\n                                                }\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"destructive\",\n                                                size: \"icon\",\n                                                className: \"absolute top-1 right-1 p-0 size-5\",\n                                                onClick: ()=>{\n                                                    handleDeleteImage(img);\n                                                },\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 37\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 25\n                            }, this),\n                            fileUpload ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_upload_cf__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                files: allFiles,\n                                setFiles: setAllFiles,\n                                accept: \"image/*\",\n                                bucketName: \"captures\",\n                                multipleUpload: true,\n                                prefix: logentryID + \"-\",\n                                displayFiles: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                        id: \"camera-video\",\n                                        style: {\n                                            display: displayImage ? \"none\" : \"block\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: image,\n                                        alt: \"Captured\",\n                                        style: {\n                                            display: displayImage ? \"block\" : \"none\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-4 gap-2\",\n                        children: [\n                            !displayImage && !fileUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: captureImage,\n                                className: \"mt-2 w-full\",\n                                children: \"Capture\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 25\n                            }, this),\n                            displayImage && !fileUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                            devices.length < 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    switchCamera();\n                                },\n                                variant: \"outline\",\n                                className: \"mt-2 w-full\",\n                                children: responsiveLabel(\"Switch Cam\", \"Switch Camera\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 608,\n                                columnNumber: 25\n                            }, this),\n                            fileUpload ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    setFileUpload(false);\n                                    handleOpenCameraDialog();\n                                },\n                                className: \"mt-2 w-full\",\n                                children: \"Capture Image\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    turnOffCamera();\n                                    setFileUpload(true);\n                                },\n                                className: \"mt-2 w-full\",\n                                children: responsiveLabel(\"Upload\", \"Upload Image\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 627,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                        lineNumber: 578,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                lineNumber: 493,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(UploadCloudFlareCaptures, \"hI3sxdLlederrl1l7Hbiq61gEeA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints,\n        _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__.useResponsiveLabel,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation\n    ];\n});\n_c = UploadCloudFlareCaptures;\nconst getCloudFlareImagesFile = (file)=>{\n    return new Promise((resolve, reject)=>{\n        if (!file || !file.name) {\n            console.error(\"No file name provided\");\n            reject(\"No file name provided\");\n            return;\n        }\n        s3Client.getObject({\n            Bucket: \"captures\",\n            Key: file.name\n        }, async (err, data)=>{\n            if (err) {\n                console.error(err);\n                reject(err);\n            } else {\n                if (!file || !file.name) {\n                    console.error(\"No file name provided\");\n                    reject(\"No file name provided\");\n                    return;\n                }\n                const fileType = file.name.split(\".\").pop() || \"\";\n                const blob = new Blob([\n                    new Uint8Array(data === null || data === void 0 ? void 0 : data.Body)\n                ]);\n                const url = URL.createObjectURL(blob);\n                if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {\n                    const base64String = Buffer.from(data === null || data === void 0 ? void 0 : data.Body).toString(\"base64\");\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    var base64Image = \"data:image/\".concat(fileType, \";base64,\").concat(base64String);\n                    if (!textContent.startsWith(\"�PNG\")) {\n                        base64Image = textContent;\n                    }\n                    resolve(base64Image);\n                } else {\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    resolve(textContent);\n                }\n            }\n        });\n    });\n};\nconst handleDeleteCFCaptureImage = (img, allImages)=>{\n    return new Promise(async (resolve, reject)=>{\n        if (!img || !img.name) {\n            console.error(\"No image name provided\");\n            reject(\"No image name provided\");\n            return;\n        }\n        s3Client.deleteObject({\n            Bucket: \"captures\",\n            Key: img.name\n        }, (err, data)=>{\n            if (err) {\n                console.error(\"Error deleting image:\", err);\n                reject(err);\n            } else {\n                resolve(allImages.filter((image)=>image.name !== img.name));\n            }\n        });\n    });\n};\nvar _c;\n$RefreshReg$(_c, \"UploadCloudFlareCaptures\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/components/upload-images.tsx\n"));

/***/ })

});