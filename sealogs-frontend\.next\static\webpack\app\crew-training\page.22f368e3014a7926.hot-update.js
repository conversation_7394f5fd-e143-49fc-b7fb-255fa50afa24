"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/crew-training-client.tsx":
/*!***********************************************************!*\
  !*** ./src/app/ui/crew-training/crew-training-client.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_filter_components_training_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/filter/components/training-actions */ \"(app-pages-browser)/./src/components/filter/components/training-actions.tsx\");\n/* harmony import */ var _app_lib_icons_SealogsTrainingIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/icons/SealogsTrainingIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsTrainingIcon.ts\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_987e738c28daf1a1a086933c5a55be9a/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useUnifiedTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingClient = ()=>{\n    _s();\n    const applyFilterRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    /** ⬅️ 1) reactive state that drives the heading */ const [isOverdueEnabled, setIsOverdueEnabled] = (0,nuqs__WEBPACK_IMPORTED_MODULE_12__.useQueryState)(\"overdue\", nuqs__WEBPACK_IMPORTED_MODULE_12__.parseAsBoolean.withDefault(false));\n    // Training data state\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [includeCompleted, setIncludeCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter options extracted from data (like reference implementation)\n    const [vesselIdOptions, setVesselIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainerIdOptions, setTrainerIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [memberIdOptions, setMemberIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Initialize permissions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.getPermissions);\n    }, []);\n    // GraphQL queries\n    const [loadTrainingSessionDues] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readTrainingSessionDues;\n            setTrainingSessionDues(((_data_readTrainingSessionDues = data.readTrainingSessionDues) === null || _data_readTrainingSessionDues === void 0 ? void 0 : _data_readTrainingSessionDues.nodes) || []);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    const [loadTrainingList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            var _response_readTrainingSessions;\n            const data = ((_response_readTrainingSessions = response.readTrainingSessions) === null || _response_readTrainingSessions === void 0 ? void 0 : _response_readTrainingSessions.nodes) || [];\n            // Transform vessel data to include complete vessel information with position (like reference)\n            const transformedData = data.map((item)=>{\n                var _item_vessel;\n                const completeVesselData = getVesselWithIcon((_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.id, item.vessel);\n                return {\n                    ...item,\n                    vessel: completeVesselData\n                };\n            });\n            // Extract filter options from data (like reference implementation)\n            const vesselIDs = Array.from(new Set(data.map((item)=>{\n                var _item_vessel;\n                return (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.id;\n            }).filter((id)=>+id !== 0)));\n            const trainingTypeIDs = Array.from(new Set(data.flatMap((item)=>{\n                var _item_trainingTypes_nodes, _item_trainingTypes;\n                return ((_item_trainingTypes = item.trainingTypes) === null || _item_trainingTypes === void 0 ? void 0 : (_item_trainingTypes_nodes = _item_trainingTypes.nodes) === null || _item_trainingTypes_nodes === void 0 ? void 0 : _item_trainingTypes_nodes.map((t)=>t.id)) || [];\n            })));\n            const trainerIDs = Array.from(new Set(data.map((item)=>item.trainerID).filter((id)=>+id !== 0)));\n            const memberIDs = Array.from(new Set(data.flatMap((item)=>{\n                var _item_members_nodes, _item_members;\n                return ((_item_members = item.members) === null || _item_members === void 0 ? void 0 : (_item_members_nodes = _item_members.nodes) === null || _item_members_nodes === void 0 ? void 0 : _item_members_nodes.map((t)=>t.id)) || [];\n            })));\n            setCompletedTrainingList(transformedData);\n            setVesselIdOptions(vesselIDs);\n            setTrainingTypeIdOptions(trainingTypeIDs);\n            setTrainerIdOptions(trainerIDs);\n            setMemberIdOptions(memberIDs);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training sessions:\", error);\n        }\n    });\n    // Vessel icon helper\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__.useVesselIconData)();\n    // Memoize the unified data calculation\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_8__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Use unified training filters hook with client-side filtering\n    const { handleFilterChange, filteredData } = (0,_hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters)({\n        initialFilter: {},\n        unifiedData\n    });\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadData = async ()=>{\n            setIsLoading(true);\n            try {\n                await Promise.all([\n                    loadTrainingSessionDues({\n                        variables: {\n                            filter: {}\n                        }\n                    }),\n                    loadTrainingList({\n                        variables: {\n                            filter: {},\n                            offset: 0,\n                            limit: 100\n                        }\n                    })\n                ]);\n            } catch (error) {\n                console.error(\"Error loading training data:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        loadData();\n    }, [\n        loadTrainingSessionDues,\n        loadTrainingList\n    ]);\n    /** ⬅️ 2) keep both the list and the heading in sync */ const handleDropdownChange = (type, data)=>{\n        var _applyFilterRef_current;\n        if (type === \"overdue\") setIsOverdueEnabled(!!data);\n        (_applyFilterRef_current = applyFilterRef.current) === null || _applyFilterRef_current === void 0 ? void 0 : _applyFilterRef_current.apply({\n            type,\n            data\n        });\n    };\n    // Handle filter changes from the FilterTable toolbar\n    const handleFilterTableChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((filterData)=>{\n        handleFilterChange(filterData);\n    }, [\n        handleFilterChange\n    ]);\n    // Check permissions - CRITICAL SECURITY GUARD\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n            lineNumber: 208,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            errorMessage: \"Oops! You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n            lineNumber: 210,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_4__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsTrainingIcon__WEBPACK_IMPORTED_MODULE_3__.SealogsTrainingIcon, {\n                    className: \"size-12\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 23\n                }, void 0),\n                title: \"Crew Trainings\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_training_actions__WEBPACK_IMPORTED_MODULE_2__.CrewTrainingFilterActions, {\n                    onChange: (data)=>{\n                        handleDropdownChange(\"overdue\", data);\n                    },\n                    overdueList: isOverdueEnabled\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                lineNumber: 216,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_5__.UnifiedTrainingTable, {\n                    unifiedData: filteredData,\n                    getVesselWithIcon: getVesselWithIcon,\n                    includeCompleted: includeCompleted,\n                    isVesselView: false,\n                    showToolbar: true,\n                    isLoading: isLoading,\n                    pageSize: 20,\n                    onChange: handleFilterTableChange,\n                    filterProps: {\n                        vesselIdOptions,\n                        trainingTypeIdOptions,\n                        trainerIdOptions,\n                        memberIdOptions\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                lineNumber: 228,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CrewTrainingClient, \"Pu7Fn5Qy3rEaKFdOBw1d3MaB4Us=\", false, function() {\n    return [\n        nuqs__WEBPACK_IMPORTED_MODULE_12__.useQueryState,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__.useVesselIconData,\n        _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters\n    ];\n});\n_c = CrewTrainingClient;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingClient);\nvar _c;\n$RefreshReg$(_c, \"CrewTrainingClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/crew-training-client.tsx\n"));

/***/ })

});