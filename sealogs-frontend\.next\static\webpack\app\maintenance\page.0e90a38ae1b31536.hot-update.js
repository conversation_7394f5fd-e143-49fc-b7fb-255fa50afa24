"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/maintenance/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/components/upload-images.tsx":
/*!*********************************************************!*\
  !*** ./src/app/ui/logbook/components/upload-images.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UploadCloudFlareCaptures; },\n/* harmony export */   getCloudFlareImagesFile: function() { return /* binding */ getCloudFlareImagesFile; },\n/* harmony export */   handleDeleteCFCaptureImage: function() { return /* binding */ handleDeleteCFCaptureImage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! aws-sdk */ \"(app-pages-browser)/./node_modules/.pnpm/aws-sdk@2.1692.0/node_modules/aws-sdk/lib/browser.js\");\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(aws_sdk__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Camera_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Camera!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../../utils/cn */ \"(app-pages-browser)/./utils/cn.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _upload_cf__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./upload-cf */ \"(app-pages-browser)/./src/app/ui/logbook/components/upload-cf.tsx\");\n/* harmony import */ var _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../../../utils/responsiveLabel */ \"(app-pages-browser)/./utils/responsiveLabel.ts\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/.pnpm/buffer@4.9.2/node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ default,getCloudFlareImagesFile,handleDeleteCFCaptureImage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ACCOUNT_ID = \"ddde1c1cd1aa25641691808dcbafdeb7\";\nconst ACCESS_KEY_ID = \"06c3e13a539f24e6fdf7075bf381bf5e\";\nconst SECRET_ACCESS_KEY = \"0bc23db8559504fb300b54def562d007e4a373fb940a7d07617ce906c553bbe8\";\nconst s3Client = new (aws_sdk__WEBPACK_IMPORTED_MODULE_2___default().S3)({\n    endpoint: \"https://\".concat(ACCOUNT_ID, \".r2.cloudflarestorage.com\"),\n    accessKeyId: ACCESS_KEY_ID,\n    secretAccessKey: SECRET_ACCESS_KEY,\n    signatureVersion: \"v4\",\n    region: \"auto\"\n});\nfunction UploadCloudFlareCaptures(param) {\n    let { file = false, setFile, inputId, buttonType = \"icon\", sectionData = {\n        id: 0,\n        sectionName: \"logBookEntryID\"\n    } } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const [openCameraDialog, setOpenCameraDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [image, setImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayImage, setDisplayImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clientID, setClientID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [devices, setDevices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fileUpload, setFileUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadFileNames, setUploadFileNames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allImages, setAllImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allFiles, setAllFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    const responsiveLabel = (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__.useResponsiveLabel)(\"phablet\");\n    const getFile = (file)=>{\n        if (!file || !file.name) {\n            console.error(\"No file name provided\");\n            return;\n        }\n        s3Client.getObject({\n            Bucket: \"captures\",\n            Key: file.name\n        }, async (err, data)=>{\n            if (err) {\n                console.error(err);\n                if (file.id) {\n                    deleteSectionMemberImage({\n                        variables: {\n                            ids: [\n                                +file.id\n                            ]\n                        }\n                    });\n                }\n            } else {\n                if (!file || !file.name) {\n                    console.error(\"No file name provided\");\n                    return;\n                }\n                const fileType = file.name.split(\".\").pop() || \"\";\n                const blob = new Blob([\n                    new Uint8Array(data === null || data === void 0 ? void 0 : data.Body)\n                ]);\n                const url = URL.createObjectURL(blob);\n                if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {\n                    // setImage(url)\n                    // setDisplayImage(true)\n                    const base64String = Buffer.from(data === null || data === void 0 ? void 0 : data.Body).toString(\"base64\");\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    var base64Image = \"data:image/\".concat(fileType, \";base64,\").concat(base64String);\n                    if (!textContent.startsWith(\"�PNG\")) {\n                        base64Image = textContent;\n                    }\n                    // setImage(base64Image)\n                    if (allImages.find((img)=>img.name === file.name) === undefined) {\n                        setAllImages((prev)=>[\n                                ...prev,\n                                {\n                                    ...file,\n                                    imageData: base64Image\n                                }\n                            ]);\n                    } else {\n                        setAllImages((prev)=>prev.map((img)=>img.name === file.name ? {\n                                    ...img,\n                                    imageData: base64Image\n                                } : img));\n                    }\n                } else {\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    // setImage(textContent)\n                    if (allImages.find((img)=>img.name === file.name) === undefined) {\n                        setAllImages((prev)=>[\n                                ...prev,\n                                {\n                                    ...file,\n                                    imageData: textContent\n                                }\n                            ]);\n                    } else {\n                        setAllImages((prev)=>prev.map((img)=>img.name === file.name ? {\n                                    ...img,\n                                    imageData: textContent\n                                } : img));\n                    }\n                // setDisplayImage(true)\n                }\n            }\n        });\n    };\n    // Handle opening the camera dialog\n    const handleOpenCameraDialog = async function() {\n        let bypass = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        setOpenCameraDialog(true);\n        setUploadFileNames(false);\n        setFileUpload(false);\n        setImage(null);\n        setDisplayImage(false);\n        if (file && file.length > 0 && !bypass) {\n            file.forEach((f)=>{\n                getFile(f);\n            });\n        // return\n        }\n        const devices = await navigator.mediaDevices.enumerateDevices();\n        const hasEnvironmentCamera = devices.some((device)=>device.kind === \"videoinput\");\n        if (hasEnvironmentCamera) {\n            setDevices(devices.filter((device)=>device.kind === \"videoinput\"));\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No camera found. Please connect a camera.\");\n            return;\n        }\n        navigator.mediaDevices.getUserMedia({\n            video: {\n                facingMode: \"environment\"\n            },\n            audio: false\n        }).then((stream)=>{\n            const videoElement = document.getElementById(\"camera-video\");\n            videoElement.srcObject = stream;\n            videoElement.play();\n        }).catch((error)=>{\n            console.error(\"Error accessing camera:\", error);\n        });\n    };\n    const captureImage = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (!videoElement) {\n            console.error(\"Video element not found\");\n            return;\n        }\n        const canvas = document.createElement(\"canvas\");\n        canvas.width = videoElement.videoWidth;\n        canvas.height = videoElement.videoHeight;\n        const context = canvas.getContext(\"2d\");\n        if (!context) {\n            console.error(\"Failed to get canvas context\");\n            return;\n        }\n        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);\n        const imageData = canvas.toDataURL(\"image/png\");\n        // Stop the camera stream after capturing the image\n        if (videoElement.srcObject) {\n            const stream = videoElement.srcObject;\n            const tracks = stream.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoElement.srcObject = null;\n        }\n        if (imageData) {\n            // setImage(imageData)\n            // setDisplayImage(true)\n            setAllImages((prev)=>[\n                    ...prev,\n                    {\n                        name: clientID + \"-capture-\" + Date.now(),\n                        imageData: imageData\n                    }\n                ]);\n            setImage(null);\n            setDisplayImage(false);\n            handleOpenCameraDialog(true);\n        }\n    };\n    const turnOffCamera = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (videoElement && videoElement.srcObject) {\n            const stream = videoElement.srcObject;\n            const tracks = stream.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoElement.srcObject = null;\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _localStorage_getItem;\n        setClientID(+((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0));\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (openCameraDialog) return;\n        turnOffCamera();\n    }, [\n        openCameraDialog\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (allFiles.length > 0) {\n            allFiles.forEach((file)=>{\n                if (file.title) {\n                    getFile({\n                        name: file.title,\n                        fieldName: inputId\n                    });\n                    setUploadFileNames((prev)=>{\n                        if (Array.isArray(prev)) {\n                            return [\n                                ...prev,\n                                file.title\n                            ];\n                        }\n                        return [\n                            file.title\n                        ];\n                    });\n                }\n            });\n        }\n    }, [\n        allFiles\n    ]);\n    const [createSectionMemberImage] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.CREATE_SECTION_MEMBER_IMAGE, {\n        onCompleted: (response)=>{\n            const data = response.createSectionMemberImage;\n            setFile();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating comment\", error);\n        }\n    });\n    async function uploadFile(file) {\n        // Upload file to Cloudflare\n        var fileName;\n        if (file.imageData) {\n            fileName = file.name || clientID + \"-capture-\" + Date.now();\n        }\n        if (!fileName) {\n            fileName = clientID + \"-capture-\" + Date.now();\n        }\n        createSectionMemberImage({\n            variables: {\n                input: {\n                    name: fileName,\n                    fieldName: inputId,\n                    imageType: \"FieldImage\",\n                    [sectionData.sectionName]: sectionData.sectionName === \"logBookEntryID\" ? logentryID : sectionData.id\n                }\n            }\n        });\n        if (file.imageData) {\n            s3Client.putObject({\n                Bucket: \"captures\",\n                Key: fileName,\n                Body: file.imageData\n            }, (err, data)=>{\n                if (err) {\n                    console.error(err);\n                } else {\n                    setFile();\n                }\n            });\n        }\n    }\n    const switchCamera = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (!videoElement) {\n            console.error(\"Video element not found\");\n            return;\n        }\n        const currentDeviceId = videoElement.srcObject ? videoElement.srcObject.getVideoTracks()[0].getSettings().deviceId : null;\n        const nextDevice = devices.find((device)=>device.kind === \"videoinput\" && device.deviceId !== currentDeviceId);\n        if (nextDevice) {\n            navigator.mediaDevices.getUserMedia({\n                video: {\n                    deviceId: nextDevice.deviceId\n                },\n                audio: false\n            }).then((stream)=>{\n                videoElement.srcObject = stream;\n                videoElement.play();\n            }).catch((error)=>{\n                console.error(\"Error switching camera:\", error);\n            });\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No other camera found to switch.\");\n        }\n    };\n    const handleUploadFile = ()=>{\n        if (allImages.length === 0) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please capture or upload an image first.\");\n            return;\n        }\n        allImages.forEach((img)=>{\n            uploadFile(img);\n        });\n        setAllImages([]);\n        setAllFiles([]);\n        setImage(null);\n        setDisplayImage(false);\n        setOpenCameraDialog(false);\n        turnOffCamera();\n        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Images uploaded successfully.\");\n    };\n    const [deleteSectionMemberImage] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.DELETE_SECTION_MEMBER_IMAGE, {\n        onCompleted: (response)=>{\n            const data = response.deleteCaptureImage;\n            if (data) {\n                setFile();\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image deleted successfully.\");\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error deleting image\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete image.\");\n        }\n    });\n    const handleDeleteImage = (img)=>{\n        setAllImages((prev)=>prev.filter((image)=>image.name !== img.name));\n        if (img.imageData) {\n            s3Client.deleteObject({\n                Bucket: \"captures\",\n                Key: img.name || \"\"\n            }, (err, data)=>{\n                if (err) {\n                    console.error(\"Error deleting image:\", err);\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image deleted successfully.\");\n                }\n            });\n            if (!img.id) {\n                return;\n            }\n            deleteSectionMemberImage({\n                variables: {\n                    ids: [\n                        +img.id\n                    ]\n                }\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: buttonType === \"icon\" ? \"ghost\" : \"outline\",\n                iconOnly: buttonType === \"icon\",\n                size: buttonType === \"icon\" ? \"icon\" : \"default\",\n                title: \"Add comment\",\n                className: buttonType === \"icon\" ? \"group\" : \"\",\n                iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: buttonType === \"icon\" ? (0,_utils_cn__WEBPACK_IMPORTED_MODULE_5__.cn)(file && file.length > 0 ? \"text-curious-blue-400 group-hover:text-curious-blue-400/50\" : \"text-outer-space-400 group-hover:text-outer-space-600\", \"will-change-transform will-change-width will-change-padding transform-gpu\", \"group-hover:transition-colors group-hover:ease-out group-hover:duration-300\") : \"\",\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 21\n                }, void 0),\n                onClick: ()=>handleOpenCameraDialog(false),\n                children: buttonType === \"button\" && (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__.getResponsiveLabel)(bp.phablet, \"Capture / Upload\", \"Capture / Upload Image\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                lineNumber: 460,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AlertDialogNew, {\n                openDialog: openCameraDialog,\n                setOpenDialog: setOpenCameraDialog,\n                size: \"lg\",\n                title: fileUpload ? \"Files\" : \"Camera\",\n                handleCreate: ()=>{\n                    // if (image) {\n                    handleUploadFile();\n                // setOpenCameraDialog(false)\n                // } else {\n                // toast.error('Please capture an image first.')\n                // }\n                },\n                handleCancel: ()=>{\n                    setOpenCameraDialog(false);\n                    setImage(null);\n                    setDisplayImage(false);\n                    setAllImages([]);\n                    turnOffCamera();\n                    setAllFiles([]);\n                    setFile();\n                },\n                actionText: \"Save\",\n                cancelText: \"Close\",\n                loading: false,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            allImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap mb-4\",\n                                children: allImages.filter((img)=>!(img === null || img === void 0 ? void 0 : img.id)).map((img, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1/4 p-1 rounded-md relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: img.imageData,\n                                                alt: \"Captured \".concat(index),\n                                                className: \"object-cover\",\n                                                onClick: ()=>{\n                                                    setImage(img.imageData);\n                                                    setDisplayImage(true);\n                                                    turnOffCamera();\n                                                }\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"destructive\",\n                                                size: \"icon\",\n                                                className: \"absolute top-1 right-1 p-0 size-5\",\n                                                onClick: ()=>{\n                                                    handleDeleteImage(img);\n                                                },\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 37\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 25\n                            }, this),\n                            fileUpload ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_upload_cf__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                files: allFiles,\n                                setFiles: setAllFiles,\n                                accept: \"image/*\",\n                                bucketName: \"captures\",\n                                multipleUpload: true,\n                                prefix: logentryID + \"-\",\n                                displayFiles: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 549,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                        id: \"camera-video\",\n                                        style: {\n                                            display: displayImage ? \"none\" : \"block\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: image,\n                                        alt: \"Captured\",\n                                        style: {\n                                            display: displayImage ? \"block\" : \"none\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                        lineNumber: 515,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-4 gap-2\",\n                        children: [\n                            !displayImage && !fileUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: captureImage,\n                                className: \"mt-2 w-full\",\n                                children: \"Capture\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 25\n                            }, this),\n                            displayImage && !fileUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                            devices.length < 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    switchCamera();\n                                },\n                                variant: \"outline\",\n                                className: \"mt-2 w-full\",\n                                children: \"Switch Camera\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 25\n                            }, this),\n                            fileUpload ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    setFileUpload(false);\n                                    handleOpenCameraDialog();\n                                },\n                                className: \"mt-2 w-full\",\n                                children: \"responsiveLabel(Capture Image)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    turnOffCamera();\n                                    setFileUpload(true);\n                                },\n                                className: \"mt-2 w-full\",\n                                children: \"Upload Image\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                lineNumber: 490,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(UploadCloudFlareCaptures, \"hI3sxdLlederrl1l7Hbiq61gEeA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints,\n        _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__.useResponsiveLabel,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation\n    ];\n});\n_c = UploadCloudFlareCaptures;\nconst getCloudFlareImagesFile = (file)=>{\n    return new Promise((resolve, reject)=>{\n        if (!file || !file.name) {\n            console.error(\"No file name provided\");\n            reject(\"No file name provided\");\n            return;\n        }\n        s3Client.getObject({\n            Bucket: \"captures\",\n            Key: file.name\n        }, async (err, data)=>{\n            if (err) {\n                console.error(err);\n                reject(err);\n            } else {\n                if (!file || !file.name) {\n                    console.error(\"No file name provided\");\n                    reject(\"No file name provided\");\n                    return;\n                }\n                const fileType = file.name.split(\".\").pop() || \"\";\n                const blob = new Blob([\n                    new Uint8Array(data === null || data === void 0 ? void 0 : data.Body)\n                ]);\n                const url = URL.createObjectURL(blob);\n                if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {\n                    const base64String = Buffer.from(data === null || data === void 0 ? void 0 : data.Body).toString(\"base64\");\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    var base64Image = \"data:image/\".concat(fileType, \";base64,\").concat(base64String);\n                    if (!textContent.startsWith(\"�PNG\")) {\n                        base64Image = textContent;\n                    }\n                    resolve(base64Image);\n                } else {\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    resolve(textContent);\n                }\n            }\n        });\n    });\n};\nconst handleDeleteCFCaptureImage = (img, allImages)=>{\n    return new Promise(async (resolve, reject)=>{\n        if (!img || !img.name) {\n            console.error(\"No image name provided\");\n            reject(\"No image name provided\");\n            return;\n        }\n        s3Client.deleteObject({\n            Bucket: \"captures\",\n            Key: img.name\n        }, (err, data)=>{\n            if (err) {\n                console.error(\"Error deleting image:\", err);\n                reject(err);\n            } else {\n                resolve(allImages.filter((image)=>image.name !== img.name));\n            }\n        });\n    });\n};\nvar _c;\n$RefreshReg$(_c, \"UploadCloudFlareCaptures\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/components/upload-images.tsx\n"));

/***/ })

});