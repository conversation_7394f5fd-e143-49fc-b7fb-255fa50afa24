"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/crew-training-client.tsx":
/*!***********************************************************!*\
  !*** ./src/app/ui/crew-training/crew-training-client.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_filter_components_training_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/filter/components/training-actions */ \"(app-pages-browser)/./src/components/filter/components/training-actions.tsx\");\n/* harmony import */ var _app_lib_icons_SealogsTrainingIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/icons/SealogsTrainingIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsTrainingIcon.ts\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_987e738c28daf1a1a086933c5a55be9a/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useUnifiedTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingClient = ()=>{\n    _s();\n    const applyFilterRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    /** ⬅️ 1) reactive state that drives the heading */ const [isOverdueEnabled, setIsOverdueEnabled] = (0,nuqs__WEBPACK_IMPORTED_MODULE_12__.useQueryState)(\"overdue\", nuqs__WEBPACK_IMPORTED_MODULE_12__.parseAsBoolean.withDefault(false));\n    // Training data state\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [includeCompleted, setIncludeCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter options extracted from data (like reference implementation)\n    const [vesselIdOptions, setVesselIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainerIdOptions, setTrainerIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [memberIdOptions, setMemberIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Initialize permissions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.getPermissions);\n    }, []);\n    // GraphQL queries\n    const [loadTrainingSessionDues] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readTrainingSessionDues;\n            setTrainingSessionDues(((_data_readTrainingSessionDues = data.readTrainingSessionDues) === null || _data_readTrainingSessionDues === void 0 ? void 0 : _data_readTrainingSessionDues.nodes) || []);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n            setHasError(true);\n        }\n    });\n    const [loadTrainingList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            var _response_readTrainingSessions;\n            const data = ((_response_readTrainingSessions = response.readTrainingSessions) === null || _response_readTrainingSessions === void 0 ? void 0 : _response_readTrainingSessions.nodes) || [];\n            // Transform vessel data to include complete vessel information with position (like reference)\n            const transformedData = data.map((item)=>{\n                var _item_vessel;\n                const completeVesselData = getVesselWithIcon((_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.id, item.vessel);\n                return {\n                    ...item,\n                    vessel: completeVesselData\n                };\n            });\n            // Extract filter options from data (like reference implementation)\n            const vesselIDs = Array.from(new Set(data.map((item)=>{\n                var _item_vessel;\n                return (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.id;\n            }).filter((id)=>+id !== 0)));\n            const trainingTypeIDs = Array.from(new Set(data.flatMap((item)=>{\n                var _item_trainingTypes_nodes, _item_trainingTypes;\n                return ((_item_trainingTypes = item.trainingTypes) === null || _item_trainingTypes === void 0 ? void 0 : (_item_trainingTypes_nodes = _item_trainingTypes.nodes) === null || _item_trainingTypes_nodes === void 0 ? void 0 : _item_trainingTypes_nodes.map((t)=>t.id)) || [];\n            })));\n            const trainerIDs = Array.from(new Set(data.map((item)=>item.trainerID).filter((id)=>+id !== 0)));\n            const memberIDs = Array.from(new Set(data.flatMap((item)=>{\n                var _item_members_nodes, _item_members;\n                return ((_item_members = item.members) === null || _item_members === void 0 ? void 0 : (_item_members_nodes = _item_members.nodes) === null || _item_members_nodes === void 0 ? void 0 : _item_members_nodes.map((t)=>t.id)) || [];\n            })));\n            setCompletedTrainingList(transformedData);\n            setVesselIdOptions(vesselIDs);\n            setTrainingTypeIdOptions(trainingTypeIDs);\n            setTrainerIdOptions(trainerIDs);\n            setMemberIdOptions(memberIDs);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training sessions:\", error);\n            setHasError(true);\n        }\n    });\n    // Vessel icon helper\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__.useVesselIconData)();\n    // Memoize the unified data calculation\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_8__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Use unified training filters hook with client-side filtering\n    const { handleFilterChange, filteredData } = (0,_hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters)({\n        initialFilter: {},\n        unifiedData\n    });\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadData = async ()=>{\n            setIsLoading(true);\n            try {\n                await Promise.all([\n                    loadTrainingSessionDues({\n                        variables: {\n                            filter: {}\n                        }\n                    }),\n                    loadTrainingList({\n                        variables: {\n                            filter: {},\n                            offset: 0,\n                            limit: 100\n                        }\n                    })\n                ]);\n            } catch (error) {\n                console.error(\"Error loading training data:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        loadData();\n    }, [\n        loadTrainingSessionDues,\n        loadTrainingList\n    ]);\n    /** ⬅️ 2) keep both the list and the heading in sync */ const handleDropdownChange = (type, data)=>{\n        var _applyFilterRef_current;\n        if (type === \"overdue\") setIsOverdueEnabled(!!data);\n        (_applyFilterRef_current = applyFilterRef.current) === null || _applyFilterRef_current === void 0 ? void 0 : _applyFilterRef_current.apply({\n            type,\n            data\n        });\n    };\n    // Handle filter changes from the FilterTable toolbar\n    const handleFilterTableChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((filterData)=>{\n        handleFilterChange(filterData);\n    }, [\n        handleFilterChange\n    ]);\n    // Check permissions - CRITICAL SECURITY GUARD\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n            lineNumber: 210,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            errorMessage: \"Oops! You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n            lineNumber: 212,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_4__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsTrainingIcon__WEBPACK_IMPORTED_MODULE_3__.SealogsTrainingIcon, {\n                    className: \"size-12\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 23\n                }, void 0),\n                title: \"Crew Trainings\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_training_actions__WEBPACK_IMPORTED_MODULE_2__.CrewTrainingFilterActions, {\n                    onChange: (data)=>{\n                        handleDropdownChange(\"overdue\", data);\n                    },\n                    overdueList: isOverdueEnabled\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                lineNumber: 218,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_5__.UnifiedTrainingTable, {\n                    unifiedData: filteredData,\n                    getVesselWithIcon: getVesselWithIcon,\n                    includeCompleted: includeCompleted,\n                    isVesselView: false,\n                    showToolbar: true,\n                    isLoading: isLoading,\n                    pageSize: 20,\n                    onChange: handleFilterTableChange,\n                    filterProps: {\n                        vesselIdOptions,\n                        trainingTypeIdOptions,\n                        trainerIdOptions,\n                        memberIdOptions\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                lineNumber: 230,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CrewTrainingClient, \"Pu7Fn5Qy3rEaKFdOBw1d3MaB4Us=\", false, function() {\n    return [\n        nuqs__WEBPACK_IMPORTED_MODULE_12__.useQueryState,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__.useVesselIconData,\n        _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters\n    ];\n});\n_c = CrewTrainingClient;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingClient);\nvar _c;\n$RefreshReg$(_c, \"CrewTrainingClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/crew-training-client.tsx\n"));

/***/ })

});