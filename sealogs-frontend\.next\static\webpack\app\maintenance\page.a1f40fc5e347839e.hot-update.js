"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/maintenance/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/components/upload-images.tsx":
/*!*********************************************************!*\
  !*** ./src/app/ui/logbook/components/upload-images.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UploadCloudFlareCaptures; },\n/* harmony export */   getCloudFlareImagesFile: function() { return /* binding */ getCloudFlareImagesFile; },\n/* harmony export */   handleDeleteCFCaptureImage: function() { return /* binding */ handleDeleteCFCaptureImage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! aws-sdk */ \"(app-pages-browser)/./node_modules/.pnpm/aws-sdk@2.1692.0/node_modules/aws-sdk/lib/browser.js\");\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(aws_sdk__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Camera_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Camera!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../../utils/cn */ \"(app-pages-browser)/./utils/cn.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _upload_cf__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./upload-cf */ \"(app-pages-browser)/./src/app/ui/logbook/components/upload-cf.tsx\");\n/* harmony import */ var _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../../../utils/responsiveLabel */ \"(app-pages-browser)/./utils/responsiveLabel.ts\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/.pnpm/buffer@4.9.2/node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ default,getCloudFlareImagesFile,handleDeleteCFCaptureImage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ACCOUNT_ID = \"ddde1c1cd1aa25641691808dcbafdeb7\";\nconst ACCESS_KEY_ID = \"06c3e13a539f24e6fdf7075bf381bf5e\";\nconst SECRET_ACCESS_KEY = \"0bc23db8559504fb300b54def562d007e4a373fb940a7d07617ce906c553bbe8\";\nconst s3Client = new (aws_sdk__WEBPACK_IMPORTED_MODULE_2___default().S3)({\n    endpoint: \"https://\".concat(ACCOUNT_ID, \".r2.cloudflarestorage.com\"),\n    accessKeyId: ACCESS_KEY_ID,\n    secretAccessKey: SECRET_ACCESS_KEY,\n    signatureVersion: \"v4\",\n    region: \"auto\"\n});\nfunction UploadCloudFlareCaptures(param) {\n    let { file = false, setFile, inputId, buttonType = \"icon\", sectionData = {\n        id: 0,\n        sectionName: \"logBookEntryID\"\n    } } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const [openCameraDialog, setOpenCameraDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [image, setImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayImage, setDisplayImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clientID, setClientID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [devices, setDevices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fileUpload, setFileUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadFileNames, setUploadFileNames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allImages, setAllImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allFiles, setAllFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    const getFile = (file)=>{\n        if (!file || !file.name) {\n            console.error(\"No file name provided\");\n            return;\n        }\n        s3Client.getObject({\n            Bucket: \"captures\",\n            Key: file.name\n        }, async (err, data)=>{\n            if (err) {\n                console.error(err);\n                if (file.id) {\n                    deleteSectionMemberImage({\n                        variables: {\n                            ids: [\n                                +file.id\n                            ]\n                        }\n                    });\n                }\n            } else {\n                if (!file || !file.name) {\n                    console.error(\"No file name provided\");\n                    return;\n                }\n                const fileType = file.name.split(\".\").pop() || \"\";\n                const blob = new Blob([\n                    new Uint8Array(data === null || data === void 0 ? void 0 : data.Body)\n                ]);\n                const url = URL.createObjectURL(blob);\n                if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {\n                    // setImage(url)\n                    // setDisplayImage(true)\n                    const base64String = Buffer.from(data === null || data === void 0 ? void 0 : data.Body).toString(\"base64\");\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    var base64Image = \"data:image/\".concat(fileType, \";base64,\").concat(base64String);\n                    if (!textContent.startsWith(\"�PNG\")) {\n                        base64Image = textContent;\n                    }\n                    // setImage(base64Image)\n                    if (allImages.find((img)=>img.name === file.name) === undefined) {\n                        setAllImages((prev)=>[\n                                ...prev,\n                                {\n                                    ...file,\n                                    imageData: base64Image\n                                }\n                            ]);\n                    } else {\n                        setAllImages((prev)=>prev.map((img)=>img.name === file.name ? {\n                                    ...img,\n                                    imageData: base64Image\n                                } : img));\n                    }\n                } else {\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    // setImage(textContent)\n                    if (allImages.find((img)=>img.name === file.name) === undefined) {\n                        setAllImages((prev)=>[\n                                ...prev,\n                                {\n                                    ...file,\n                                    imageData: textContent\n                                }\n                            ]);\n                    } else {\n                        setAllImages((prev)=>prev.map((img)=>img.name === file.name ? {\n                                    ...img,\n                                    imageData: textContent\n                                } : img));\n                    }\n                // setDisplayImage(true)\n                }\n            }\n        });\n    };\n    // Handle opening the camera dialog\n    const handleOpenCameraDialog = async function() {\n        let bypass = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        setOpenCameraDialog(true);\n        setUploadFileNames(false);\n        setFileUpload(false);\n        setImage(null);\n        setDisplayImage(false);\n        if (file && file.length > 0 && !bypass) {\n            file.forEach((f)=>{\n                getFile(f);\n            });\n        // return\n        }\n        const devices = await navigator.mediaDevices.enumerateDevices();\n        const hasEnvironmentCamera = devices.some((device)=>device.kind === \"videoinput\");\n        if (hasEnvironmentCamera) {\n            setDevices(devices.filter((device)=>device.kind === \"videoinput\"));\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No camera found. Please connect a camera.\");\n            return;\n        }\n        navigator.mediaDevices.getUserMedia({\n            video: {\n                facingMode: \"environment\"\n            },\n            audio: false\n        }).then((stream)=>{\n            const videoElement = document.getElementById(\"camera-video\");\n            videoElement.srcObject = stream;\n            videoElement.play();\n        }).catch((error)=>{\n            console.error(\"Error accessing camera:\", error);\n        });\n    };\n    const captureImage = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (!videoElement) {\n            console.error(\"Video element not found\");\n            return;\n        }\n        const canvas = document.createElement(\"canvas\");\n        canvas.width = videoElement.videoWidth;\n        canvas.height = videoElement.videoHeight;\n        const context = canvas.getContext(\"2d\");\n        if (!context) {\n            console.error(\"Failed to get canvas context\");\n            return;\n        }\n        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);\n        const imageData = canvas.toDataURL(\"image/png\");\n        // Stop the camera stream after capturing the image\n        if (videoElement.srcObject) {\n            const stream = videoElement.srcObject;\n            const tracks = stream.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoElement.srcObject = null;\n        }\n        if (imageData) {\n            // setImage(imageData)\n            // setDisplayImage(true)\n            setAllImages((prev)=>[\n                    ...prev,\n                    {\n                        name: clientID + \"-capture-\" + Date.now(),\n                        imageData: imageData\n                    }\n                ]);\n            setImage(null);\n            setDisplayImage(false);\n            handleOpenCameraDialog(true);\n        }\n    };\n    const turnOffCamera = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (videoElement && videoElement.srcObject) {\n            const stream = videoElement.srcObject;\n            const tracks = stream.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoElement.srcObject = null;\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _localStorage_getItem;\n        setClientID(+((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0));\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (openCameraDialog) return;\n        turnOffCamera();\n    }, [\n        openCameraDialog\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (allFiles.length > 0) {\n            allFiles.forEach((file)=>{\n                if (file.title) {\n                    getFile({\n                        name: file.title,\n                        fieldName: inputId\n                    });\n                    setUploadFileNames((prev)=>{\n                        if (Array.isArray(prev)) {\n                            return [\n                                ...prev,\n                                file.title\n                            ];\n                        }\n                        return [\n                            file.title\n                        ];\n                    });\n                }\n            });\n        }\n    }, [\n        allFiles\n    ]);\n    const [createSectionMemberImage] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.CREATE_SECTION_MEMBER_IMAGE, {\n        onCompleted: (response)=>{\n            const data = response.createSectionMemberImage;\n            setFile();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating comment\", error);\n        }\n    });\n    async function uploadFile(file) {\n        // Upload file to Cloudflare\n        var fileName;\n        if (file.imageData) {\n            fileName = file.name || clientID + \"-capture-\" + Date.now();\n        }\n        if (!fileName) {\n            fileName = clientID + \"-capture-\" + Date.now();\n        }\n        createSectionMemberImage({\n            variables: {\n                input: {\n                    name: fileName,\n                    fieldName: inputId,\n                    imageType: \"FieldImage\",\n                    [sectionData.sectionName]: sectionData.sectionName === \"logBookEntryID\" ? logentryID : sectionData.id\n                }\n            }\n        });\n        if (file.imageData) {\n            s3Client.putObject({\n                Bucket: \"captures\",\n                Key: fileName,\n                Body: file.imageData\n            }, (err, data)=>{\n                if (err) {\n                    console.error(err);\n                } else {\n                    setFile();\n                }\n            });\n        }\n    }\n    const switchCamera = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (!videoElement) {\n            console.error(\"Video element not found\");\n            return;\n        }\n        const currentDeviceId = videoElement.srcObject ? videoElement.srcObject.getVideoTracks()[0].getSettings().deviceId : null;\n        const nextDevice = devices.find((device)=>device.kind === \"videoinput\" && device.deviceId !== currentDeviceId);\n        if (nextDevice) {\n            navigator.mediaDevices.getUserMedia({\n                video: {\n                    deviceId: nextDevice.deviceId\n                },\n                audio: false\n            }).then((stream)=>{\n                videoElement.srcObject = stream;\n                videoElement.play();\n            }).catch((error)=>{\n                console.error(\"Error switching camera:\", error);\n            });\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No other camera found to switch.\");\n        }\n    };\n    const handleUploadFile = ()=>{\n        if (allImages.length === 0) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please capture or upload an image first.\");\n            return;\n        }\n        allImages.forEach((img)=>{\n            uploadFile(img);\n        });\n        setAllImages([]);\n        setAllFiles([]);\n        setImage(null);\n        setDisplayImage(false);\n        setOpenCameraDialog(false);\n        turnOffCamera();\n        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Images uploaded successfully.\");\n    };\n    const [deleteSectionMemberImage] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.DELETE_SECTION_MEMBER_IMAGE, {\n        onCompleted: (response)=>{\n            const data = response.deleteCaptureImage;\n            if (data) {\n                setFile();\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image deleted successfully.\");\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error deleting image\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete image.\");\n        }\n    });\n    const handleDeleteImage = (img)=>{\n        setAllImages((prev)=>prev.filter((image)=>image.name !== img.name));\n        if (img.imageData) {\n            s3Client.deleteObject({\n                Bucket: \"captures\",\n                Key: img.name || \"\"\n            }, (err, data)=>{\n                if (err) {\n                    console.error(\"Error deleting image:\", err);\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image deleted successfully.\");\n                }\n            });\n            if (!img.id) {\n                return;\n            }\n            deleteSectionMemberImage({\n                variables: {\n                    ids: [\n                        +img.id\n                    ]\n                }\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: buttonType === \"icon\" ? \"ghost\" : \"outline\",\n                iconOnly: buttonType === \"icon\",\n                size: buttonType === \"icon\" ? \"icon\" : \"default\",\n                title: \"Add comment\",\n                className: buttonType === \"icon\" ? \"group\" : \"\",\n                iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: buttonType === \"icon\" ? (0,_utils_cn__WEBPACK_IMPORTED_MODULE_5__.cn)(file && file.length > 0 ? \"text-curious-blue-400 group-hover:text-curious-blue-400/50\" : \"text-outer-space-400 group-hover:text-outer-space-600\", \"will-change-transform will-change-width will-change-padding transform-gpu\", \"group-hover:transition-colors group-hover:ease-out group-hover:duration-300\") : \"\",\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 21\n                }, void 0),\n                onClick: ()=>handleOpenCameraDialog(false),\n                children: buttonType === \"button\" && (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__.getResponsiveLabel)(bp.phablet, \"Capture / Upload\", \"Capture / Upload Image\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                lineNumber: 458,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AlertDialogNew, {\n                openDialog: openCameraDialog,\n                setOpenDialog: setOpenCameraDialog,\n                size: \"lg\",\n                title: fileUpload ? \"Files\" : \"Camera\",\n                handleCreate: ()=>{\n                    // if (image) {\n                    handleUploadFile();\n                // setOpenCameraDialog(false)\n                // } else {\n                // toast.error('Please capture an image first.')\n                // }\n                },\n                handleCancel: ()=>{\n                    setOpenCameraDialog(false);\n                    setImage(null);\n                    setDisplayImage(false);\n                    setAllImages([]);\n                    turnOffCamera();\n                    setAllFiles([]);\n                    setFile();\n                },\n                actionText: \"Save\",\n                cancelText: \"Close\",\n                loading: false,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            allImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap mb-4\",\n                                children: allImages.filter((img)=>!(img === null || img === void 0 ? void 0 : img.id)).map((img, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1/4 p-1 rounded-md relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: img.imageData,\n                                                alt: \"Captured \".concat(index),\n                                                className: \"object-cover\",\n                                                onClick: ()=>{\n                                                    setImage(img.imageData);\n                                                    setDisplayImage(true);\n                                                    turnOffCamera();\n                                                }\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"destructive\",\n                                                size: \"icon\",\n                                                className: \"absolute top-1 right-1 p-0 size-5\",\n                                                onClick: ()=>{\n                                                    handleDeleteImage(img);\n                                                },\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 37\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 25\n                            }, this),\n                            fileUpload ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_upload_cf__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                files: allFiles,\n                                setFiles: setAllFiles,\n                                accept: \"image/*\",\n                                bucketName: \"captures\",\n                                multipleUpload: true,\n                                prefix: logentryID + \"-\",\n                                displayFiles: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 547,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                        id: \"camera-video\",\n                                        style: {\n                                            display: displayImage ? \"none\" : \"block\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: image,\n                                        alt: \"Captured\",\n                                        style: {\n                                            display: displayImage ? \"block\" : \"none\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                        lineNumber: 513,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-4 gap-2 justify-between\",\n                        children: [\n                            !displayImage && !fileUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: captureImage,\n                                className: \"mt-2\",\n                                children: \"Capture\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 575,\n                                columnNumber: 25\n                            }, this),\n                            displayImage && !fileUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                            !devices.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    switchCamera();\n                                },\n                                variant: \"outline\",\n                                className: \"mt-2\",\n                                children: \"Switch Camera\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 25\n                            }, this),\n                            fileUpload ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    setFileUpload(false);\n                                    handleOpenCameraDialog();\n                                },\n                                className: \"mt-2\",\n                                children: \"Capture Image\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 613,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    turnOffCamera();\n                                    setFileUpload(true);\n                                },\n                                className: \"mt-2\",\n                                children: \"Upload Image\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                        lineNumber: 573,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                lineNumber: 488,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(UploadCloudFlareCaptures, \"tO+FzjAwrNpn7Czrp7cxVnPeGmE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation\n    ];\n});\n_c = UploadCloudFlareCaptures;\nconst getCloudFlareImagesFile = (file)=>{\n    return new Promise((resolve, reject)=>{\n        if (!file || !file.name) {\n            console.error(\"No file name provided\");\n            reject(\"No file name provided\");\n            return;\n        }\n        s3Client.getObject({\n            Bucket: \"captures\",\n            Key: file.name\n        }, async (err, data)=>{\n            if (err) {\n                console.error(err);\n                reject(err);\n            } else {\n                if (!file || !file.name) {\n                    console.error(\"No file name provided\");\n                    reject(\"No file name provided\");\n                    return;\n                }\n                const fileType = file.name.split(\".\").pop() || \"\";\n                const blob = new Blob([\n                    new Uint8Array(data === null || data === void 0 ? void 0 : data.Body)\n                ]);\n                const url = URL.createObjectURL(blob);\n                if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {\n                    const base64String = Buffer.from(data === null || data === void 0 ? void 0 : data.Body).toString(\"base64\");\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    var base64Image = \"data:image/\".concat(fileType, \";base64,\").concat(base64String);\n                    if (!textContent.startsWith(\"�PNG\")) {\n                        base64Image = textContent;\n                    }\n                    resolve(base64Image);\n                } else {\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    resolve(textContent);\n                }\n            }\n        });\n    });\n};\nconst handleDeleteCFCaptureImage = (img, allImages)=>{\n    return new Promise(async (resolve, reject)=>{\n        if (!img || !img.name) {\n            console.error(\"No image name provided\");\n            reject(\"No image name provided\");\n            return;\n        }\n        s3Client.deleteObject({\n            Bucket: \"captures\",\n            Key: img.name\n        }, (err, data)=>{\n            if (err) {\n                console.error(\"Error deleting image:\", err);\n                reject(err);\n            } else {\n                resolve(allImages.filter((image)=>image.name !== img.name));\n            }\n        });\n    });\n};\nvar _c;\n$RefreshReg$(_c, \"UploadCloudFlareCaptures\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/components/upload-images.tsx\n"));

/***/ })

});