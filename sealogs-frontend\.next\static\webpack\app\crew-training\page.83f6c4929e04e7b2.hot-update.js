"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/crew-training-client.tsx":
/*!***********************************************************!*\
  !*** ./src/app/ui/crew-training/crew-training-client.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_filter_components_training_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/filter/components/training-actions */ \"(app-pages-browser)/./src/components/filter/components/training-actions.tsx\");\n/* harmony import */ var _app_lib_icons_SealogsTrainingIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/icons/SealogsTrainingIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsTrainingIcon.ts\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_987e738c28daf1a1a086933c5a55be9a/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useUnifiedTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingClient = ()=>{\n    _s();\n    const applyFilterRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    /** ⬅️ 1) reactive state that drives the heading */ const [isOverdueEnabled, setIsOverdueEnabled] = (0,nuqs__WEBPACK_IMPORTED_MODULE_13__.useQueryState)(\"overdue\", nuqs__WEBPACK_IMPORTED_MODULE_13__.parseAsBoolean.withDefault(false));\n    // Training data state\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [includeCompleted, setIncludeCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // GraphQL queries\n    const [loadTrainingSessionDues] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes;\n            if (data) {\n                // Filter out crew members who are no longer assigned to the vessel (same as list.tsx)\n                const filteredData = data.filter((item)=>item.vessel.seaLogsMembers.nodes.some((m)=>{\n                        return m.id === item.memberID;\n                    }));\n                const dueWithStatus = filteredData.map((due)=>{\n                    return {\n                        ...due,\n                        status: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_12__.GetTrainingSessionStatus)(due)\n                    };\n                });\n                // Group dues by vessel, training type, and due date (same logic as list.tsx)\n                const groupedDues = dueWithStatus.reduce((acc, due)=>{\n                    const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n                    if (!acc[key]) {\n                        var _due_trainingSession;\n                        acc[key] = {\n                            id: due.id,\n                            vesselID: due.vesselID,\n                            vessel: due.vessel,\n                            trainingTypeID: due.trainingTypeID,\n                            trainingType: due.trainingType,\n                            dueDate: due.dueDate,\n                            status: due.status,\n                            trainingLocationType: (_due_trainingSession = due.trainingSession) === null || _due_trainingSession === void 0 ? void 0 : _due_trainingSession.trainingLocationType,\n                            members: []\n                        };\n                    }\n                    acc[key].members.push(due.member);\n                    return acc;\n                }, {});\n                const mergedDues = Object.values(groupedDues).map((group)=>{\n                    const mergedMembers = group.members.reduce((acc, member)=>{\n                        const existingMember = acc.find((m)=>m.id === member.id);\n                        if (existingMember) {\n                            existingMember.firstName = member.firstName;\n                            existingMember.surname = member.surname;\n                        } else {\n                            acc.push(member);\n                        }\n                        return acc;\n                    }, []);\n                    return {\n                        id: group.id,\n                        vesselID: group.vesselID,\n                        vessel: group.vessel,\n                        trainingTypeID: group.trainingTypeID,\n                        trainingType: group.trainingType,\n                        status: group.status,\n                        dueDate: group.dueDate,\n                        trainingLocationType: group.trainingLocationType,\n                        members: mergedMembers\n                    };\n                });\n                setTrainingSessionDues(mergedDues);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    const [loadTrainingList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__.TRAINING_SESSIONS, {\n        onCompleted: (data)=>{\n            var _data_readTrainingSessions;\n            setCompletedTrainingList(((_data_readTrainingSessions = data.readTrainingSessions) === null || _data_readTrainingSessions === void 0 ? void 0 : _data_readTrainingSessions.nodes) || []);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training sessions:\", error);\n        }\n    });\n    // Vessel icon helper\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__.useVesselIconData)();\n    // Memoize the unified data calculation\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_8__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Use unified training filters hook with client-side filtering\n    const { handleFilterChange, filteredData } = (0,_hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters)({\n        initialFilter: {},\n        unifiedData\n    });\n    // Initialize permissions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.getPermissions);\n    }, []);\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadData = async ()=>{\n            setIsLoading(true);\n            try {\n                await Promise.all([\n                    loadTrainingSessionDues({\n                        variables: {\n                            filter: {}\n                        }\n                    }),\n                    loadTrainingList({\n                        variables: {\n                            filter: {},\n                            offset: 0,\n                            limit: 100\n                        }\n                    })\n                ]);\n            } catch (error) {\n                console.error(\"Error loading training data:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        loadData();\n    }, [\n        loadTrainingSessionDues,\n        loadTrainingList\n    ]);\n    /** ⬅️ 2) keep both the list and the heading in sync */ const handleDropdownChange = (type, data)=>{\n        var _applyFilterRef_current;\n        if (type === \"overdue\") setIsOverdueEnabled(!!data);\n        (_applyFilterRef_current = applyFilterRef.current) === null || _applyFilterRef_current === void 0 ? void 0 : _applyFilterRef_current.apply({\n            type,\n            data\n        });\n    };\n    // Handle filter changes from the FilterTable toolbar\n    const handleFilterTableChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((filterData)=>{\n        handleFilterChange(filterData);\n    }, [\n        handleFilterChange\n    ]);\n    // Permission guards - same as in list.tsx\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n            lineNumber: 207,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n            lineNumber: 209,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_4__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsTrainingIcon__WEBPACK_IMPORTED_MODULE_3__.SealogsTrainingIcon, {\n                    className: \"size-12\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 23\n                }, void 0),\n                title: \"Crew Trainings\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_training_actions__WEBPACK_IMPORTED_MODULE_2__.CrewTrainingFilterActions, {\n                    onChange: (data)=>{\n                        handleDropdownChange(\"overdue\", data);\n                    },\n                    overdueList: isOverdueEnabled\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                lineNumber: 215,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_5__.UnifiedTrainingTable, {\n                    unifiedData: filteredData,\n                    getVesselWithIcon: getVesselWithIcon,\n                    includeCompleted: includeCompleted,\n                    isVesselView: false,\n                    showToolbar: true,\n                    isLoading: isLoading,\n                    pageSize: 20,\n                    onChange: handleFilterTableChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                lineNumber: 227,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CrewTrainingClient, \"8eW10xk4JjGpvhU7hxsExxQ2PpU=\", false, function() {\n    return [\n        nuqs__WEBPACK_IMPORTED_MODULE_13__.useQueryState,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__.useVesselIconData,\n        _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters\n    ];\n});\n_c = CrewTrainingClient;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingClient);\nvar _c;\n$RefreshReg$(_c, \"CrewTrainingClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvY3Jldy10cmFpbmluZy9jcmV3LXRyYWluaW5nLWNsaWVudC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVnRjtBQUdXO0FBQ2xCO0FBQ2pCO0FBQ0o7QUFDVztBQUNsQjtBQUliO0FBQ2dDO0FBQytCO0FBQ2xCO0FBQ0w7QUFDckM7QUFDeUI7QUFRNUQsTUFBTXNCLHFCQUFxQjs7SUFDdkIsTUFBTUMsaUJBQWlCdEIsNkNBQU1BLENBQWU7SUFFNUMsaURBQWlELEdBQ2pELE1BQU0sQ0FBQ3VCLGtCQUFrQkMsb0JBQW9CLEdBQUdoQixvREFBYUEsQ0FDekQsV0FDQUMsaURBQWNBLENBQUNnQixXQUFXLENBQUM7SUFHL0Isc0JBQXNCO0lBQ3RCLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHMUIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDMkIscUJBQXFCQyx1QkFBdUIsR0FBRzVCLCtDQUFRQSxDQUFRLEVBQUU7SUFDeEUsTUFBTSxDQUFDNkIsdUJBQXVCQyx5QkFBeUIsR0FBRzlCLCtDQUFRQSxDQUM5RCxFQUFFO0lBRU4sTUFBTSxDQUFDK0Isa0JBQWtCQyxvQkFBb0IsR0FBR2hDLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ2lDLGFBQWFDLGVBQWUsR0FBR2xDLCtDQUFRQSxDQUFNO0lBRXBELGtCQUFrQjtJQUNsQixNQUFNLENBQUNtQyx3QkFBd0IsR0FBR3pCLDZEQUFZQSxDQUFDRSw4RUFBMEJBLEVBQUU7UUFDdkV3QixhQUFhO1FBQ2JDLGFBQWEsQ0FBQ0M7WUFDVixNQUFNQyxPQUFPRCxTQUFTRSx1QkFBdUIsQ0FBQ0MsS0FBSztZQUNuRCxJQUFJRixNQUFNO2dCQUNOLHNGQUFzRjtnQkFDdEYsTUFBTUcsZUFBZUgsS0FBS0ksTUFBTSxDQUFDLENBQUNDLE9BQzlCQSxLQUFLQyxNQUFNLENBQUNDLGNBQWMsQ0FBQ0wsS0FBSyxDQUFDTSxJQUFJLENBQUMsQ0FBQ0M7d0JBQ25DLE9BQU9BLEVBQUVDLEVBQUUsS0FBS0wsS0FBS00sUUFBUTtvQkFDakM7Z0JBRUosTUFBTUMsZ0JBQWdCVCxhQUFhVSxHQUFHLENBQUMsQ0FBQ0M7b0JBQ3BDLE9BQU87d0JBQUUsR0FBR0EsR0FBRzt3QkFBRUMsUUFBUW5DLDJFQUF3QkEsQ0FBQ2tDO29CQUFLO2dCQUMzRDtnQkFFQSw2RUFBNkU7Z0JBQzdFLE1BQU1FLGNBQWNKLGNBQWNLLE1BQU0sQ0FDcEMsQ0FBQ0MsS0FBVUo7b0JBQ1AsTUFBTUssTUFBTSxHQUFtQkwsT0FBaEJBLElBQUlNLFFBQVEsRUFBQyxLQUF5Qk4sT0FBdEJBLElBQUlPLGNBQWMsRUFBQyxLQUFlLE9BQVpQLElBQUlRLE9BQU87b0JBQ2hFLElBQUksQ0FBQ0osR0FBRyxDQUFDQyxJQUFJLEVBQUU7NEJBVUhMO3dCQVRSSSxHQUFHLENBQUNDLElBQUksR0FBRzs0QkFDUFQsSUFBSUksSUFBSUosRUFBRTs0QkFDVlUsVUFBVU4sSUFBSU0sUUFBUTs0QkFDdEJkLFFBQVFRLElBQUlSLE1BQU07NEJBQ2xCZSxnQkFBZ0JQLElBQUlPLGNBQWM7NEJBQ2xDRSxjQUFjVCxJQUFJUyxZQUFZOzRCQUM5QkQsU0FBU1IsSUFBSVEsT0FBTzs0QkFDcEJQLFFBQVFELElBQUlDLE1BQU07NEJBQ2xCUyxvQkFBb0IsR0FDaEJWLHVCQUFBQSxJQUFJVyxlQUFlLGNBQW5CWCwyQ0FBQUEscUJBQXFCVSxvQkFBb0I7NEJBQzdDRSxTQUFTLEVBQUU7d0JBQ2Y7b0JBQ0o7b0JBQ0FSLEdBQUcsQ0FBQ0MsSUFBSSxDQUFDTyxPQUFPLENBQUNDLElBQUksQ0FBQ2IsSUFBSWMsTUFBTTtvQkFDaEMsT0FBT1Y7Z0JBQ1gsR0FDQSxDQUFDO2dCQUdMLE1BQU1XLGFBQWFDLE9BQU9DLE1BQU0sQ0FBQ2YsYUFBYUgsR0FBRyxDQUM3QyxDQUFDbUI7b0JBQ0csTUFBTUMsZ0JBQWdCRCxNQUFNTixPQUFPLENBQUNULE1BQU0sQ0FDdEMsQ0FBQ0MsS0FBVVU7d0JBQ1AsTUFBTU0saUJBQWlCaEIsSUFBSWlCLElBQUksQ0FDM0IsQ0FBQzFCLElBQVdBLEVBQUVDLEVBQUUsS0FBS2tCLE9BQU9sQixFQUFFO3dCQUVsQyxJQUFJd0IsZ0JBQWdCOzRCQUNoQkEsZUFBZUUsU0FBUyxHQUFHUixPQUFPUSxTQUFTOzRCQUMzQ0YsZUFBZUcsT0FBTyxHQUFHVCxPQUFPUyxPQUFPO3dCQUMzQyxPQUFPOzRCQUNIbkIsSUFBSVMsSUFBSSxDQUFDQzt3QkFDYjt3QkFDQSxPQUFPVjtvQkFDWCxHQUNBLEVBQUU7b0JBRU4sT0FBTzt3QkFDSFIsSUFBSXNCLE1BQU10QixFQUFFO3dCQUNaVSxVQUFVWSxNQUFNWixRQUFRO3dCQUN4QmQsUUFBUTBCLE1BQU0xQixNQUFNO3dCQUNwQmUsZ0JBQWdCVyxNQUFNWCxjQUFjO3dCQUNwQ0UsY0FBY1MsTUFBTVQsWUFBWTt3QkFDaENSLFFBQVFpQixNQUFNakIsTUFBTTt3QkFDcEJPLFNBQVNVLE1BQU1WLE9BQU87d0JBQ3RCRSxzQkFBc0JRLE1BQU1SLG9CQUFvQjt3QkFDaERFLFNBQVNPO29CQUNiO2dCQUNKO2dCQUVKNUMsdUJBQXVCd0M7WUFDM0I7UUFDSjtRQUNBUyxTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FBQyx3Q0FBd0NBO1FBQzFEO0lBQ0o7SUFFQSxNQUFNLENBQUNFLGlCQUFpQixHQUFHdEUsNkRBQVlBLENBQUNDLHFFQUFpQkEsRUFBRTtRQUN2RDBCLGFBQWEsQ0FBQ0U7Z0JBQ2VBO1lBQXpCVCx5QkFBeUJTLEVBQUFBLDZCQUFBQSxLQUFLMEMsb0JBQW9CLGNBQXpCMUMsaURBQUFBLDJCQUEyQkUsS0FBSyxLQUFJLEVBQUU7UUFDbkU7UUFDQW9DLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLG9DQUFvQ0E7UUFDdEQ7SUFDSjtJQUVBLHFCQUFxQjtJQUNyQixNQUFNLEVBQUVJLGlCQUFpQixFQUFFLEdBQUdyRSw4RUFBaUJBO0lBRS9DLHVDQUF1QztJQUN2QyxNQUFNc0UsY0FBY2pGLDhDQUFPQSxDQUFDO1FBQ3hCLE9BQU9ZLDZHQUE0QkEsQ0FBQztZQUNoQ2E7WUFDQUU7WUFDQXFEO1lBQ0FuRDtRQUNKO0lBQ0osR0FBRztRQUNDSjtRQUNBRTtRQUNBcUQ7UUFDQW5EO0tBQ0g7SUFFRCwrREFBK0Q7SUFDL0QsTUFBTSxFQUFFcUQsa0JBQWtCLEVBQUUxQyxZQUFZLEVBQUUsR0FBRzNCLDJGQUF5QkEsQ0FBQztRQUNuRXNFLGVBQWUsQ0FBQztRQUNoQkY7SUFDSjtJQUVBLHlCQUF5QjtJQUN6QmxGLGdEQUFTQSxDQUFDO1FBQ05pQyxlQUFlbEIsb0VBQWNBO0lBQ2pDLEdBQUcsRUFBRTtJQUVMLCtCQUErQjtJQUMvQmYsZ0RBQVNBLENBQUM7UUFDTixNQUFNcUYsV0FBVztZQUNiNUQsYUFBYTtZQUNiLElBQUk7Z0JBQ0EsTUFBTTZELFFBQVFDLEdBQUcsQ0FBQztvQkFDZHJELHdCQUF3Qjt3QkFBRXNELFdBQVc7NEJBQUU5QyxRQUFRLENBQUM7d0JBQUU7b0JBQUU7b0JBQ3BEcUMsaUJBQWlCO3dCQUNiUyxXQUFXOzRCQUFFOUMsUUFBUSxDQUFDOzRCQUFHK0MsUUFBUTs0QkFBR0MsT0FBTzt3QkFBSTtvQkFDbkQ7aUJBQ0g7WUFDTCxFQUFFLE9BQU9iLE9BQU87Z0JBQ1pDLFFBQVFELEtBQUssQ0FBQyxnQ0FBZ0NBO1lBQ2xELFNBQVU7Z0JBQ05wRCxhQUFhO1lBQ2pCO1FBQ0o7UUFFQTREO0lBQ0osR0FBRztRQUFDbkQ7UUFBeUI2QztLQUFpQjtJQUU5QyxxREFBcUQsR0FDckQsTUFBTVksdUJBQXVCLENBQUNDLE1BQWN0RDtZQUV4Q2xCO1FBREEsSUFBSXdFLFNBQVMsV0FBV3RFLG9CQUFvQixDQUFDLENBQUNnQjtTQUM5Q2xCLDBCQUFBQSxlQUFleUUsT0FBTyxjQUF0QnpFLDhDQUFBQSx3QkFBd0IwRSxLQUFLLENBQUM7WUFBRUY7WUFBTXREO1FBQUs7SUFDL0M7SUFFQSxxREFBcUQ7SUFDckQsTUFBTXlELDBCQUEwQjdGLGtEQUFXQSxDQUN2QyxDQUFDOEY7UUFDR2IsbUJBQW1CYTtJQUN2QixHQUNBO1FBQUNiO0tBQW1CO0lBR3hCLDBDQUEwQztJQUMxQyxJQUNJLENBQUNuRCxlQUNBLENBQUNoQix1RUFBYUEsQ0FBQyxpQkFBaUJnQixnQkFDN0IsQ0FBQ2hCLHVFQUFhQSxDQUFDLGlCQUFpQmdCLGdCQUNoQyxDQUFDaEIsdUVBQWFBLENBQUMsbUJBQW1CZ0IsZ0JBQ2xDLENBQUNoQix1RUFBYUEsQ0FBQyx3QkFBd0JnQixjQUM3QztRQUNFLE9BQU8sQ0FBQ0EsNEJBQ0osOERBQUNmLHFEQUFPQTs7OztzQ0FFUiw4REFBQ0EscURBQU9BO1lBQUNnRixjQUFhOzs7Ozs7SUFFOUI7SUFFQSxxQkFDSTs7MEJBQ0ksOERBQUM1RixrRUFBVUE7Z0JBQ1A2RixvQkFBTSw4REFBQzlGLG1GQUFtQkE7b0JBQUMrRixXQUFVOzs7Ozs7Z0JBQ3JDQyxPQUFPO2dCQUNQQyx1QkFDSSw4REFBQ2xHLHFHQUF5QkE7b0JBQ3RCbUcsVUFBVSxDQUFDaEU7d0JBQ1BxRCxxQkFBcUIsV0FBV3JEO29CQUNwQztvQkFDQWlFLGFBQWFsRjs7Ozs7Ozs7Ozs7MEJBSXpCLDhEQUFDbUY7Z0JBQUlMLFdBQVU7MEJBQ1gsNEVBQUMzRix5RUFBb0JBO29CQUNqQjBFLGFBQWF6QztvQkFDYndDLG1CQUFtQkE7b0JBQ25CbkQsa0JBQWtCQTtvQkFDbEIyRSxjQUFjO29CQUNkQyxhQUFhO29CQUNibEYsV0FBV0E7b0JBQ1htRixVQUFVO29CQUNWTCxVQUFVUDs7Ozs7Ozs7Ozs7OztBQUs5QjtHQXBOTTVFOztRQUk4Q2IsZ0RBQWFBO1FBZTNCRyx5REFBWUE7UUE2RW5CQSx5REFBWUE7UUFVVEcsMEVBQWlCQTtRQWtCRkUsdUZBQXlCQTs7O0tBNUhwRUs7QUFzTk4sK0RBQWVBLGtCQUFrQkEsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL3VpL2NyZXctdHJhaW5pbmcvY3Jldy10cmFpbmluZy1jbGllbnQudHN4P2ZiMzYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgdXNlUmVmLCB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VNZW1vLCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0J1xyXG5pbXBvcnQgQ3Jld1RyYWluaW5nTGlzdCBmcm9tICcuL2xpc3QnXHJcblxyXG5pbXBvcnQgeyBDcmV3VHJhaW5pbmdGaWx0ZXJBY3Rpb25zIH0gZnJvbSAnQC9jb21wb25lbnRzL2ZpbHRlci9jb21wb25lbnRzL3RyYWluaW5nLWFjdGlvbnMnXHJcbmltcG9ydCB7IFNlYWxvZ3NUcmFpbmluZ0ljb24gfSBmcm9tICdAL2FwcC9saWIvaWNvbnMvU2VhbG9nc1RyYWluaW5nSWNvbidcclxuaW1wb3J0IHsgTGlzdEhlYWRlciB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9saXN0LWhlYWRlcidcclxuaW1wb3J0IHsgdXNlUXVlcnlTdGF0ZSwgcGFyc2VBc0Jvb2xlYW4gfSBmcm9tICdudXFzJ1xyXG5pbXBvcnQgeyBVbmlmaWVkVHJhaW5pbmdUYWJsZSB9IGZyb20gJy4vdW5pZmllZC10cmFpbmluZy10YWJsZSdcclxuaW1wb3J0IHsgdXNlTGF6eVF1ZXJ5IH0gZnJvbSAnQGFwb2xsby9jbGllbnQnXHJcbmltcG9ydCB7XHJcbiAgICBUUkFJTklOR19TRVNTSU9OUyxcclxuICAgIFJFQURfVFJBSU5JTkdfU0VTU0lPTl9EVUVTLFxyXG59IGZyb20gJ0AvYXBwL2xpYi9ncmFwaFFML3F1ZXJ5J1xyXG5pbXBvcnQgeyB1c2VWZXNzZWxJY29uRGF0YSB9IGZyb20gJ0AvYXBwL2xpYi92ZXNzZWwtaWNvbi1oZWxwZXInXHJcbmltcG9ydCB7IG1lcmdlQW5kU29ydENyZXdUcmFpbmluZ0RhdGEgfSBmcm9tICdAL2FwcC91aS9jcmV3LXRyYWluaW5nL3V0aWxzL2NyZXctdHJhaW5pbmctdXRpbHMnXHJcbmltcG9ydCB7IHVzZVVuaWZpZWRUcmFpbmluZ0ZpbHRlcnMgfSBmcm9tICcuL2hvb2tzL3VzZVVuaWZpZWRUcmFpbmluZ0ZpbHRlcnMnXHJcbmltcG9ydCB7IGdldFBlcm1pc3Npb25zLCBoYXNQZXJtaXNzaW9uIH0gZnJvbSAnQC9hcHAvaGVscGVycy91c2VySGVscGVyJ1xyXG5pbXBvcnQgTG9hZGluZyBmcm9tICdAL2FwcC9sb2FkaW5nJ1xyXG5pbXBvcnQgeyBHZXRUcmFpbmluZ1Nlc3Npb25TdGF0dXMgfSBmcm9tICdAL2FwcC9saWIvYWN0aW9ucydcclxuXHJcbnR5cGUgRmlsdGVySGFuZGxlID0ge1xyXG4gICAgYXBwbHk6IChwOiB7IHR5cGU6IHN0cmluZzsgZGF0YTogYW55IH0pID0+IHZvaWRcclxuICAgIG92ZXJkdWU6IGJvb2xlYW4gLy8gcmVhZC1vbmx5IHNuYXBzaG90XHJcbiAgICBzZXRPdmVyZHVlOiAodjogYm9vbGVhbikgPT4gdm9pZFxyXG59XHJcblxyXG5jb25zdCBDcmV3VHJhaW5pbmdDbGllbnQgPSAoKSA9PiB7XHJcbiAgICBjb25zdCBhcHBseUZpbHRlclJlZiA9IHVzZVJlZjxGaWx0ZXJIYW5kbGU+KG51bGwpXHJcblxyXG4gICAgLyoqIOKshe+4jyAxKSByZWFjdGl2ZSBzdGF0ZSB0aGF0IGRyaXZlcyB0aGUgaGVhZGluZyAqL1xyXG4gICAgY29uc3QgW2lzT3ZlcmR1ZUVuYWJsZWQsIHNldElzT3ZlcmR1ZUVuYWJsZWRdID0gdXNlUXVlcnlTdGF0ZShcclxuICAgICAgICAnb3ZlcmR1ZScsXHJcbiAgICAgICAgcGFyc2VBc0Jvb2xlYW4ud2l0aERlZmF1bHQoZmFsc2UpLFxyXG4gICAgKVxyXG5cclxuICAgIC8vIFRyYWluaW5nIGRhdGEgc3RhdGVcclxuICAgIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcclxuICAgIGNvbnN0IFt0cmFpbmluZ1Nlc3Npb25EdWVzLCBzZXRUcmFpbmluZ1Nlc3Npb25EdWVzXSA9IHVzZVN0YXRlPGFueVtdPihbXSlcclxuICAgIGNvbnN0IFtjb21wbGV0ZWRUcmFpbmluZ0xpc3QsIHNldENvbXBsZXRlZFRyYWluaW5nTGlzdF0gPSB1c2VTdGF0ZTxhbnlbXT4oXHJcbiAgICAgICAgW10sXHJcbiAgICApXHJcbiAgICBjb25zdCBbaW5jbHVkZUNvbXBsZXRlZCwgc2V0SW5jbHVkZUNvbXBsZXRlZF0gPSB1c2VTdGF0ZSh0cnVlKVxyXG4gICAgY29uc3QgW3Blcm1pc3Npb25zLCBzZXRQZXJtaXNzaW9uc10gPSB1c2VTdGF0ZTxhbnk+KGZhbHNlKVxyXG5cclxuICAgIC8vIEdyYXBoUUwgcXVlcmllc1xyXG4gICAgY29uc3QgW2xvYWRUcmFpbmluZ1Nlc3Npb25EdWVzXSA9IHVzZUxhenlRdWVyeShSRUFEX1RSQUlOSU5HX1NFU1NJT05fRFVFUywge1xyXG4gICAgICAgIGZldGNoUG9saWN5OiAnY2FjaGUtYW5kLW5ldHdvcmsnLFxyXG4gICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2UpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLnJlYWRUcmFpbmluZ1Nlc3Npb25EdWVzLm5vZGVzXHJcbiAgICAgICAgICAgIGlmIChkYXRhKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBGaWx0ZXIgb3V0IGNyZXcgbWVtYmVycyB3aG8gYXJlIG5vIGxvbmdlciBhc3NpZ25lZCB0byB0aGUgdmVzc2VsIChzYW1lIGFzIGxpc3QudHN4KVxyXG4gICAgICAgICAgICAgICAgY29uc3QgZmlsdGVyZWREYXRhID0gZGF0YS5maWx0ZXIoKGl0ZW06IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICBpdGVtLnZlc3NlbC5zZWFMb2dzTWVtYmVycy5ub2Rlcy5zb21lKChtOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG0uaWQgPT09IGl0ZW0ubWVtYmVySURcclxuICAgICAgICAgICAgICAgICAgICB9KSxcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIGNvbnN0IGR1ZVdpdGhTdGF0dXMgPSBmaWx0ZXJlZERhdGEubWFwKChkdWU6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB7IC4uLmR1ZSwgc3RhdHVzOiBHZXRUcmFpbmluZ1Nlc3Npb25TdGF0dXMoZHVlKSB9XHJcbiAgICAgICAgICAgICAgICB9KVxyXG5cclxuICAgICAgICAgICAgICAgIC8vIEdyb3VwIGR1ZXMgYnkgdmVzc2VsLCB0cmFpbmluZyB0eXBlLCBhbmQgZHVlIGRhdGUgKHNhbWUgbG9naWMgYXMgbGlzdC50c3gpXHJcbiAgICAgICAgICAgICAgICBjb25zdCBncm91cGVkRHVlcyA9IGR1ZVdpdGhTdGF0dXMucmVkdWNlKFxyXG4gICAgICAgICAgICAgICAgICAgIChhY2M6IGFueSwgZHVlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qga2V5ID0gYCR7ZHVlLnZlc3NlbElEfS0ke2R1ZS50cmFpbmluZ1R5cGVJRH0tJHtkdWUuZHVlRGF0ZX1gXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghYWNjW2tleV0pIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjY1trZXldID0ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBkdWUuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsSUQ6IGR1ZS52ZXNzZWxJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWw6IGR1ZS52ZXNzZWwsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlSUQ6IGR1ZS50cmFpbmluZ1R5cGVJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ1R5cGU6IGR1ZS50cmFpbmluZ1R5cGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHVlRGF0ZTogZHVlLmR1ZURhdGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiBkdWUuc3RhdHVzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nTG9jYXRpb25UeXBlOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdWUudHJhaW5pbmdTZXNzaW9uPy50cmFpbmluZ0xvY2F0aW9uVHlwZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXJzOiBbXSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBhY2Nba2V5XS5tZW1iZXJzLnB1c2goZHVlLm1lbWJlcilcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGFjY1xyXG4gICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAge30sXHJcbiAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgY29uc3QgbWVyZ2VkRHVlcyA9IE9iamVjdC52YWx1ZXMoZ3JvdXBlZER1ZXMpLm1hcChcclxuICAgICAgICAgICAgICAgICAgICAoZ3JvdXA6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBtZXJnZWRNZW1iZXJzID0gZ3JvdXAubWVtYmVycy5yZWR1Y2UoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAoYWNjOiBhbnksIG1lbWJlcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdNZW1iZXIgPSBhY2MuZmluZChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKG06IGFueSkgPT4gbS5pZCA9PT0gbWVtYmVyLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZXhpc3RpbmdNZW1iZXIpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhpc3RpbmdNZW1iZXIuZmlyc3ROYW1lID0gbWVtYmVyLmZpcnN0TmFtZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBleGlzdGluZ01lbWJlci5zdXJuYW1lID0gbWVtYmVyLnN1cm5hbWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY2MucHVzaChtZW1iZXIpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBhY2NcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBbXSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IGdyb3VwLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsSUQ6IGdyb3VwLnZlc3NlbElELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsOiBncm91cC52ZXNzZWwsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ1R5cGVJRDogZ3JvdXAudHJhaW5pbmdUeXBlSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ1R5cGU6IGdyb3VwLnRyYWluaW5nVHlwZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1czogZ3JvdXAuc3RhdHVzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZHVlRGF0ZTogZ3JvdXAuZHVlRGF0ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nTG9jYXRpb25UeXBlOiBncm91cC50cmFpbmluZ0xvY2F0aW9uVHlwZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlcnM6IG1lcmdlZE1lbWJlcnMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgc2V0VHJhaW5pbmdTZXNzaW9uRHVlcyhtZXJnZWREdWVzKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgICBvbkVycm9yOiAoZXJyb3IpID0+IHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyB0cmFpbmluZyBzZXNzaW9uIGR1ZXM6JywgZXJyb3IpXHJcbiAgICAgICAgfSxcclxuICAgIH0pXHJcblxyXG4gICAgY29uc3QgW2xvYWRUcmFpbmluZ0xpc3RdID0gdXNlTGF6eVF1ZXJ5KFRSQUlOSU5HX1NFU1NJT05TLCB7XHJcbiAgICAgICAgb25Db21wbGV0ZWQ6IChkYXRhKSA9PiB7XHJcbiAgICAgICAgICAgIHNldENvbXBsZXRlZFRyYWluaW5nTGlzdChkYXRhLnJlYWRUcmFpbmluZ1Nlc3Npb25zPy5ub2RlcyB8fCBbXSlcclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIHRyYWluaW5nIHNlc3Npb25zOicsIGVycm9yKVxyXG4gICAgICAgIH0sXHJcbiAgICB9KVxyXG5cclxuICAgIC8vIFZlc3NlbCBpY29uIGhlbHBlclxyXG4gICAgY29uc3QgeyBnZXRWZXNzZWxXaXRoSWNvbiB9ID0gdXNlVmVzc2VsSWNvbkRhdGEoKVxyXG5cclxuICAgIC8vIE1lbW9pemUgdGhlIHVuaWZpZWQgZGF0YSBjYWxjdWxhdGlvblxyXG4gICAgY29uc3QgdW5pZmllZERhdGEgPSB1c2VNZW1vKCgpID0+IHtcclxuICAgICAgICByZXR1cm4gbWVyZ2VBbmRTb3J0Q3Jld1RyYWluaW5nRGF0YSh7XHJcbiAgICAgICAgICAgIHRyYWluaW5nU2Vzc2lvbkR1ZXMsXHJcbiAgICAgICAgICAgIGNvbXBsZXRlZFRyYWluaW5nTGlzdCxcclxuICAgICAgICAgICAgZ2V0VmVzc2VsV2l0aEljb24sXHJcbiAgICAgICAgICAgIGluY2x1ZGVDb21wbGV0ZWQsXHJcbiAgICAgICAgfSlcclxuICAgIH0sIFtcclxuICAgICAgICB0cmFpbmluZ1Nlc3Npb25EdWVzLFxyXG4gICAgICAgIGNvbXBsZXRlZFRyYWluaW5nTGlzdCxcclxuICAgICAgICBnZXRWZXNzZWxXaXRoSWNvbixcclxuICAgICAgICBpbmNsdWRlQ29tcGxldGVkLFxyXG4gICAgXSlcclxuXHJcbiAgICAvLyBVc2UgdW5pZmllZCB0cmFpbmluZyBmaWx0ZXJzIGhvb2sgd2l0aCBjbGllbnQtc2lkZSBmaWx0ZXJpbmdcclxuICAgIGNvbnN0IHsgaGFuZGxlRmlsdGVyQ2hhbmdlLCBmaWx0ZXJlZERhdGEgfSA9IHVzZVVuaWZpZWRUcmFpbmluZ0ZpbHRlcnMoe1xyXG4gICAgICAgIGluaXRpYWxGaWx0ZXI6IHt9LFxyXG4gICAgICAgIHVuaWZpZWREYXRhLFxyXG4gICAgfSlcclxuXHJcbiAgICAvLyBJbml0aWFsaXplIHBlcm1pc3Npb25zXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIHNldFBlcm1pc3Npb25zKGdldFBlcm1pc3Npb25zKVxyXG4gICAgfSwgW10pXHJcblxyXG4gICAgLy8gTG9hZCBkYXRhIG9uIGNvbXBvbmVudCBtb3VudFxyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBjb25zdCBsb2FkRGF0YSA9IGFzeW5jICgpID0+IHtcclxuICAgICAgICAgICAgc2V0SXNMb2FkaW5nKHRydWUpXHJcbiAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgICBhd2FpdCBQcm9taXNlLmFsbChbXHJcbiAgICAgICAgICAgICAgICAgICAgbG9hZFRyYWluaW5nU2Vzc2lvbkR1ZXMoeyB2YXJpYWJsZXM6IHsgZmlsdGVyOiB7fSB9IH0pLFxyXG4gICAgICAgICAgICAgICAgICAgIGxvYWRUcmFpbmluZ0xpc3Qoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHsgZmlsdGVyOiB7fSwgb2Zmc2V0OiAwLCBsaW1pdDogMTAwIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgfSksXHJcbiAgICAgICAgICAgICAgICBdKVxyXG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyB0cmFpbmluZyBkYXRhOicsIGVycm9yKVxyXG4gICAgICAgICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBsb2FkRGF0YSgpXHJcbiAgICB9LCBbbG9hZFRyYWluaW5nU2Vzc2lvbkR1ZXMsIGxvYWRUcmFpbmluZ0xpc3RdKVxyXG5cclxuICAgIC8qKiDirIXvuI8gMikga2VlcCBib3RoIHRoZSBsaXN0IGFuZCB0aGUgaGVhZGluZyBpbiBzeW5jICovXHJcbiAgICBjb25zdCBoYW5kbGVEcm9wZG93bkNoYW5nZSA9ICh0eXBlOiBzdHJpbmcsIGRhdGE6IGFueSkgPT4ge1xyXG4gICAgICAgIGlmICh0eXBlID09PSAnb3ZlcmR1ZScpIHNldElzT3ZlcmR1ZUVuYWJsZWQoISFkYXRhKVxyXG4gICAgICAgIGFwcGx5RmlsdGVyUmVmLmN1cnJlbnQ/LmFwcGx5KHsgdHlwZSwgZGF0YSB9KVxyXG4gICAgfVxyXG5cclxuICAgIC8vIEhhbmRsZSBmaWx0ZXIgY2hhbmdlcyBmcm9tIHRoZSBGaWx0ZXJUYWJsZSB0b29sYmFyXHJcbiAgICBjb25zdCBoYW5kbGVGaWx0ZXJUYWJsZUNoYW5nZSA9IHVzZUNhbGxiYWNrKFxyXG4gICAgICAgIChmaWx0ZXJEYXRhOiB7IHR5cGU6IHN0cmluZzsgZGF0YTogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgaGFuZGxlRmlsdGVyQ2hhbmdlKGZpbHRlckRhdGEpXHJcbiAgICAgICAgfSxcclxuICAgICAgICBbaGFuZGxlRmlsdGVyQ2hhbmdlXSxcclxuICAgIClcclxuXHJcbiAgICAvLyBQZXJtaXNzaW9uIGd1YXJkcyAtIHNhbWUgYXMgaW4gbGlzdC50c3hcclxuICAgIGlmIChcclxuICAgICAgICAhcGVybWlzc2lvbnMgfHxcclxuICAgICAgICAoIWhhc1Blcm1pc3Npb24oJ0VESVRfVFJBSU5JTkcnLCBwZXJtaXNzaW9ucykgJiZcclxuICAgICAgICAgICAgIWhhc1Blcm1pc3Npb24oJ1ZJRVdfVFJBSU5JTkcnLCBwZXJtaXNzaW9ucykgJiZcclxuICAgICAgICAgICAgIWhhc1Blcm1pc3Npb24oJ1JFQ09SRF9UUkFJTklORycsIHBlcm1pc3Npb25zKSAmJlxyXG4gICAgICAgICAgICAhaGFzUGVybWlzc2lvbignVklFV19NRU1CRVJfVFJBSU5JTkcnLCBwZXJtaXNzaW9ucykpXHJcbiAgICApIHtcclxuICAgICAgICByZXR1cm4gIXBlcm1pc3Npb25zID8gKFxyXG4gICAgICAgICAgICA8TG9hZGluZyAvPlxyXG4gICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgIDxMb2FkaW5nIGVycm9yTWVzc2FnZT1cIk9vcHMgWW91IGRvIG5vdCBoYXZlIHRoZSBwZXJtaXNzaW9uIHRvIHZpZXcgdGhpcyBzZWN0aW9uLlwiIC8+XHJcbiAgICAgICAgKVxyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPD5cclxuICAgICAgICAgICAgPExpc3RIZWFkZXJcclxuICAgICAgICAgICAgICAgIGljb249ezxTZWFsb2dzVHJhaW5pbmdJY29uIGNsYXNzTmFtZT1cInNpemUtMTJcIiAvPn1cclxuICAgICAgICAgICAgICAgIHRpdGxlPXsnQ3JldyBUcmFpbmluZ3MnfVxyXG4gICAgICAgICAgICAgICAgYWN0aW9ucz17XHJcbiAgICAgICAgICAgICAgICAgICAgPENyZXdUcmFpbmluZ0ZpbHRlckFjdGlvbnNcclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhkYXRhOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZURyb3Bkb3duQ2hhbmdlKCdvdmVyZHVlJywgZGF0YSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb3ZlcmR1ZUxpc3Q9e2lzT3ZlcmR1ZUVuYWJsZWR9XHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xNlwiPlxyXG4gICAgICAgICAgICAgICAgPFVuaWZpZWRUcmFpbmluZ1RhYmxlXHJcbiAgICAgICAgICAgICAgICAgICAgdW5pZmllZERhdGE9e2ZpbHRlcmVkRGF0YX1cclxuICAgICAgICAgICAgICAgICAgICBnZXRWZXNzZWxXaXRoSWNvbj17Z2V0VmVzc2VsV2l0aEljb259XHJcbiAgICAgICAgICAgICAgICAgICAgaW5jbHVkZUNvbXBsZXRlZD17aW5jbHVkZUNvbXBsZXRlZH1cclxuICAgICAgICAgICAgICAgICAgICBpc1Zlc3NlbFZpZXc9e2ZhbHNlfVxyXG4gICAgICAgICAgICAgICAgICAgIHNob3dUb29sYmFyPXt0cnVlfVxyXG4gICAgICAgICAgICAgICAgICAgIGlzTG9hZGluZz17aXNMb2FkaW5nfVxyXG4gICAgICAgICAgICAgICAgICAgIHBhZ2VTaXplPXsyMH1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlRmlsdGVyVGFibGVDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8Lz5cclxuICAgIClcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgQ3Jld1RyYWluaW5nQ2xpZW50XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVJlZiIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlTWVtbyIsInVzZUNhbGxiYWNrIiwiQ3Jld1RyYWluaW5nRmlsdGVyQWN0aW9ucyIsIlNlYWxvZ3NUcmFpbmluZ0ljb24iLCJMaXN0SGVhZGVyIiwidXNlUXVlcnlTdGF0ZSIsInBhcnNlQXNCb29sZWFuIiwiVW5pZmllZFRyYWluaW5nVGFibGUiLCJ1c2VMYXp5UXVlcnkiLCJUUkFJTklOR19TRVNTSU9OUyIsIlJFQURfVFJBSU5JTkdfU0VTU0lPTl9EVUVTIiwidXNlVmVzc2VsSWNvbkRhdGEiLCJtZXJnZUFuZFNvcnRDcmV3VHJhaW5pbmdEYXRhIiwidXNlVW5pZmllZFRyYWluaW5nRmlsdGVycyIsImdldFBlcm1pc3Npb25zIiwiaGFzUGVybWlzc2lvbiIsIkxvYWRpbmciLCJHZXRUcmFpbmluZ1Nlc3Npb25TdGF0dXMiLCJDcmV3VHJhaW5pbmdDbGllbnQiLCJhcHBseUZpbHRlclJlZiIsImlzT3ZlcmR1ZUVuYWJsZWQiLCJzZXRJc092ZXJkdWVFbmFibGVkIiwid2l0aERlZmF1bHQiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJ0cmFpbmluZ1Nlc3Npb25EdWVzIiwic2V0VHJhaW5pbmdTZXNzaW9uRHVlcyIsImNvbXBsZXRlZFRyYWluaW5nTGlzdCIsInNldENvbXBsZXRlZFRyYWluaW5nTGlzdCIsImluY2x1ZGVDb21wbGV0ZWQiLCJzZXRJbmNsdWRlQ29tcGxldGVkIiwicGVybWlzc2lvbnMiLCJzZXRQZXJtaXNzaW9ucyIsImxvYWRUcmFpbmluZ1Nlc3Npb25EdWVzIiwiZmV0Y2hQb2xpY3kiLCJvbkNvbXBsZXRlZCIsInJlc3BvbnNlIiwiZGF0YSIsInJlYWRUcmFpbmluZ1Nlc3Npb25EdWVzIiwibm9kZXMiLCJmaWx0ZXJlZERhdGEiLCJmaWx0ZXIiLCJpdGVtIiwidmVzc2VsIiwic2VhTG9nc01lbWJlcnMiLCJzb21lIiwibSIsImlkIiwibWVtYmVySUQiLCJkdWVXaXRoU3RhdHVzIiwibWFwIiwiZHVlIiwic3RhdHVzIiwiZ3JvdXBlZER1ZXMiLCJyZWR1Y2UiLCJhY2MiLCJrZXkiLCJ2ZXNzZWxJRCIsInRyYWluaW5nVHlwZUlEIiwiZHVlRGF0ZSIsInRyYWluaW5nVHlwZSIsInRyYWluaW5nTG9jYXRpb25UeXBlIiwidHJhaW5pbmdTZXNzaW9uIiwibWVtYmVycyIsInB1c2giLCJtZW1iZXIiLCJtZXJnZWREdWVzIiwiT2JqZWN0IiwidmFsdWVzIiwiZ3JvdXAiLCJtZXJnZWRNZW1iZXJzIiwiZXhpc3RpbmdNZW1iZXIiLCJmaW5kIiwiZmlyc3ROYW1lIiwic3VybmFtZSIsIm9uRXJyb3IiLCJlcnJvciIsImNvbnNvbGUiLCJsb2FkVHJhaW5pbmdMaXN0IiwicmVhZFRyYWluaW5nU2Vzc2lvbnMiLCJnZXRWZXNzZWxXaXRoSWNvbiIsInVuaWZpZWREYXRhIiwiaGFuZGxlRmlsdGVyQ2hhbmdlIiwiaW5pdGlhbEZpbHRlciIsImxvYWREYXRhIiwiUHJvbWlzZSIsImFsbCIsInZhcmlhYmxlcyIsIm9mZnNldCIsImxpbWl0IiwiaGFuZGxlRHJvcGRvd25DaGFuZ2UiLCJ0eXBlIiwiY3VycmVudCIsImFwcGx5IiwiaGFuZGxlRmlsdGVyVGFibGVDaGFuZ2UiLCJmaWx0ZXJEYXRhIiwiZXJyb3JNZXNzYWdlIiwiaWNvbiIsImNsYXNzTmFtZSIsInRpdGxlIiwiYWN0aW9ucyIsIm9uQ2hhbmdlIiwib3ZlcmR1ZUxpc3QiLCJkaXYiLCJpc1Zlc3NlbFZpZXciLCJzaG93VG9vbGJhciIsInBhZ2VTaXplIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/crew-training-client.tsx\n"));

/***/ })

});