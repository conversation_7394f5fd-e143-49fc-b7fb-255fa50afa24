"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/crew-training-client.tsx":
/*!***********************************************************!*\
  !*** ./src/app/ui/crew-training/crew-training-client.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_filter_components_training_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/filter/components/training-actions */ \"(app-pages-browser)/./src/components/filter/components/training-actions.tsx\");\n/* harmony import */ var _app_lib_icons_SealogsTrainingIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/icons/SealogsTrainingIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsTrainingIcon.ts\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_987e738c28daf1a1a086933c5a55be9a/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useUnifiedTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingClient = ()=>{\n    _s();\n    const applyFilterRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    /** ⬅️ 1) reactive state that drives the heading */ const [isOverdueEnabled, setIsOverdueEnabled] = (0,nuqs__WEBPACK_IMPORTED_MODULE_12__.useQueryState)(\"overdue\", nuqs__WEBPACK_IMPORTED_MODULE_12__.parseAsBoolean.withDefault(false));\n    // Training data state\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [includeCompleted, setIncludeCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter options extracted from data (like reference implementation)\n    const [vesselIdOptions, setVesselIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [trainerIdOptions, setTrainerIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [memberIdOptions, setMemberIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Initialize permissions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.getPermissions);\n    }, []);\n    // GraphQL queries\n    const [loadTrainingSessionDues] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readTrainingSessionDues;\n            setTrainingSessionDues(((_data_readTrainingSessionDues = data.readTrainingSessionDues) === null || _data_readTrainingSessionDues === void 0 ? void 0 : _data_readTrainingSessionDues.nodes) || []);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    const [loadTrainingList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_6__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            var _response_readTrainingSessions;\n            const data = ((_response_readTrainingSessions = response.readTrainingSessions) === null || _response_readTrainingSessions === void 0 ? void 0 : _response_readTrainingSessions.nodes) || [];\n            // Transform vessel data to include complete vessel information with position (like reference)\n            const transformedData = data.map((item)=>{\n                var _item_vessel;\n                const completeVesselData = getVesselWithIcon((_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.id, item.vessel);\n                return {\n                    ...item,\n                    vessel: completeVesselData\n                };\n            });\n            // Extract filter options from data (like reference implementation)\n            const vesselIDs = Array.from(new Set(data.map((item)=>{\n                var _item_vessel;\n                return (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.id;\n            }).filter((id)=>+id !== 0)));\n            const trainingTypeIDs = Array.from(new Set(data.flatMap((item)=>{\n                var _item_trainingTypes_nodes, _item_trainingTypes;\n                return ((_item_trainingTypes = item.trainingTypes) === null || _item_trainingTypes === void 0 ? void 0 : (_item_trainingTypes_nodes = _item_trainingTypes.nodes) === null || _item_trainingTypes_nodes === void 0 ? void 0 : _item_trainingTypes_nodes.map((t)=>t.id)) || [];\n            })));\n            const trainerIDs = Array.from(new Set(data.map((item)=>item.trainerID).filter((id)=>+id !== 0)));\n            const memberIDs = Array.from(new Set(data.flatMap((item)=>{\n                var _item_members_nodes, _item_members;\n                return ((_item_members = item.members) === null || _item_members === void 0 ? void 0 : (_item_members_nodes = _item_members.nodes) === null || _item_members_nodes === void 0 ? void 0 : _item_members_nodes.map((t)=>t.id)) || [];\n            })));\n            setCompletedTrainingList(transformedData);\n            setVesselIdOptions(vesselIDs);\n            setTrainingTypeIdOptions(trainingTypeIDs);\n            setTrainerIdOptions(trainerIDs);\n            setMemberIdOptions(memberIDs);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training sessions:\", error);\n        }\n    });\n    // Vessel icon helper\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__.useVesselIconData)();\n    // Memoize the unified data calculation\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_8__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Use unified training filters hook with client-side filtering\n    const { handleFilterChange, filteredData } = (0,_hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters)({\n        initialFilter: {},\n        unifiedData\n    });\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadData = async ()=>{\n            setIsLoading(true);\n            try {\n                await Promise.all([\n                    loadTrainingSessionDues({\n                        variables: {\n                            filter: {}\n                        }\n                    }),\n                    loadTrainingList({\n                        variables: {\n                            filter: {},\n                            offset: 0,\n                            limit: 100\n                        }\n                    })\n                ]);\n            } catch (error) {\n                console.error(\"Error loading training data:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        loadData();\n    }, [\n        loadTrainingSessionDues,\n        loadTrainingList\n    ]);\n    /** ⬅️ 2) keep both the list and the heading in sync */ const handleDropdownChange = (type, data)=>{\n        var _applyFilterRef_current;\n        if (type === \"overdue\") setIsOverdueEnabled(!!data);\n        (_applyFilterRef_current = applyFilterRef.current) === null || _applyFilterRef_current === void 0 ? void 0 : _applyFilterRef_current.apply({\n            type,\n            data\n        });\n    };\n    // Handle filter changes from the FilterTable toolbar\n    const handleFilterTableChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((filterData)=>{\n        handleFilterChange(filterData);\n    }, [\n        handleFilterChange\n    ]);\n    // Check permissions - CRITICAL SECURITY GUARD\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n            lineNumber: 207,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            errorMessage: \"Oops! You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n            lineNumber: 209,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_4__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsTrainingIcon__WEBPACK_IMPORTED_MODULE_3__.SealogsTrainingIcon, {\n                    className: \"size-12\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 23\n                }, void 0),\n                title: \"Crew Trainings\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_training_actions__WEBPACK_IMPORTED_MODULE_2__.CrewTrainingFilterActions, {\n                    onChange: (data)=>{\n                        handleDropdownChange(\"overdue\", data);\n                    },\n                    overdueList: isOverdueEnabled\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                lineNumber: 215,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_5__.UnifiedTrainingTable, {\n                    unifiedData: filteredData,\n                    getVesselWithIcon: getVesselWithIcon,\n                    includeCompleted: includeCompleted,\n                    isVesselView: false,\n                    showToolbar: true,\n                    isLoading: isLoading,\n                    pageSize: 20,\n                    onChange: handleFilterTableChange,\n                    filterProps: {\n                        vesselIdOptions,\n                        trainingTypeIdOptions,\n                        trainerIdOptions,\n                        memberIdOptions\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\crew-training-client.tsx\",\n                lineNumber: 227,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CrewTrainingClient, \"QWR530QsarIMbkGNavi3MMzsKp0=\", false, function() {\n    return [\n        nuqs__WEBPACK_IMPORTED_MODULE_12__.useQueryState,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_7__.useVesselIconData,\n        _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters\n    ];\n});\n_c = CrewTrainingClient;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingClient);\nvar _c;\n$RefreshReg$(_c, \"CrewTrainingClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/crew-training-client.tsx\n"));

/***/ })

});