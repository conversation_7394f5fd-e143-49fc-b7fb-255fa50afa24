"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/components/upload-images.tsx":
/*!*********************************************************!*\
  !*** ./src/app/ui/logbook/components/upload-images.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UploadCloudFlareCaptures; },\n/* harmony export */   getCloudFlareImagesFile: function() { return /* binding */ getCloudFlareImagesFile; },\n/* harmony export */   handleDeleteCFCaptureImage: function() { return /* binding */ handleDeleteCFCaptureImage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! aws-sdk */ \"(app-pages-browser)/./node_modules/.pnpm/aws-sdk@2.1692.0/node_modules/aws-sdk/lib/browser.js\");\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(aws_sdk__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Camera_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Camera!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../../utils/cn */ \"(app-pages-browser)/./utils/cn.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _upload_cf__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./upload-cf */ \"(app-pages-browser)/./src/app/ui/logbook/components/upload-cf.tsx\");\n/* harmony import */ var _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../../../utils/responsiveLabel */ \"(app-pages-browser)/./utils/responsiveLabel.ts\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/.pnpm/buffer@4.9.2/node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ default,getCloudFlareImagesFile,handleDeleteCFCaptureImage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ACCOUNT_ID = \"ddde1c1cd1aa25641691808dcbafdeb7\";\nconst ACCESS_KEY_ID = \"06c3e13a539f24e6fdf7075bf381bf5e\";\nconst SECRET_ACCESS_KEY = \"0bc23db8559504fb300b54def562d007e4a373fb940a7d07617ce906c553bbe8\";\nconst s3Client = new (aws_sdk__WEBPACK_IMPORTED_MODULE_2___default().S3)({\n    endpoint: \"https://\".concat(ACCOUNT_ID, \".r2.cloudflarestorage.com\"),\n    accessKeyId: ACCESS_KEY_ID,\n    secretAccessKey: SECRET_ACCESS_KEY,\n    signatureVersion: \"v4\",\n    region: \"auto\"\n});\nfunction UploadCloudFlareCaptures(param) {\n    let { file = false, setFile, inputId, buttonType = \"icon\", sectionData = {\n        id: 0,\n        sectionName: \"logBookEntryID\"\n    } } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const [openCameraDialog, setOpenCameraDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [image, setImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayImage, setDisplayImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clientID, setClientID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [devices, setDevices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fileUpload, setFileUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadFileNames, setUploadFileNames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allImages, setAllImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allFiles, setAllFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    const responsiveLabel = (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__.useResponsiveLabel)(\"phablet\");\n    const getFile = (file)=>{\n        if (!file || !file.name) {\n            console.error(\"No file name provided\");\n            return;\n        }\n        s3Client.getObject({\n            Bucket: \"captures\",\n            Key: file.name\n        }, async (err, data)=>{\n            if (err) {\n                console.error(err);\n                if (file.id) {\n                    deleteSectionMemberImage({\n                        variables: {\n                            ids: [\n                                +file.id\n                            ]\n                        }\n                    });\n                }\n            } else {\n                if (!file || !file.name) {\n                    console.error(\"No file name provided\");\n                    return;\n                }\n                const fileType = file.name.split(\".\").pop() || \"\";\n                const blob = new Blob([\n                    new Uint8Array(data === null || data === void 0 ? void 0 : data.Body)\n                ]);\n                const url = URL.createObjectURL(blob);\n                if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {\n                    // setImage(url)\n                    // setDisplayImage(true)\n                    const base64String = Buffer.from(data === null || data === void 0 ? void 0 : data.Body).toString(\"base64\");\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    var base64Image = \"data:image/\".concat(fileType, \";base64,\").concat(base64String);\n                    if (!textContent.startsWith(\"�PNG\")) {\n                        base64Image = textContent;\n                    }\n                    // setImage(base64Image)\n                    if (allImages.find((img)=>img.name === file.name) === undefined) {\n                        setAllImages((prev)=>[\n                                ...prev,\n                                {\n                                    ...file,\n                                    imageData: base64Image\n                                }\n                            ]);\n                    } else {\n                        setAllImages((prev)=>prev.map((img)=>img.name === file.name ? {\n                                    ...img,\n                                    imageData: base64Image\n                                } : img));\n                    }\n                } else {\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    // setImage(textContent)\n                    if (allImages.find((img)=>img.name === file.name) === undefined) {\n                        setAllImages((prev)=>[\n                                ...prev,\n                                {\n                                    ...file,\n                                    imageData: textContent\n                                }\n                            ]);\n                    } else {\n                        setAllImages((prev)=>prev.map((img)=>img.name === file.name ? {\n                                    ...img,\n                                    imageData: textContent\n                                } : img));\n                    }\n                // setDisplayImage(true)\n                }\n            }\n        });\n    };\n    // Handle opening the camera dialog\n    const handleOpenCameraDialog = async function() {\n        let bypass = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        setOpenCameraDialog(true);\n        setUploadFileNames(false);\n        setFileUpload(false);\n        setImage(null);\n        setDisplayImage(false);\n        if (file && file.length > 0 && !bypass) {\n            file.forEach((f)=>{\n                getFile(f);\n            });\n        // return\n        }\n        const devices = await navigator.mediaDevices.enumerateDevices();\n        const hasEnvironmentCamera = devices.some((device)=>device.kind === \"videoinput\");\n        if (hasEnvironmentCamera) {\n            setDevices(devices.filter((device)=>device.kind === \"videoinput\"));\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No camera found. Please connect a camera.\");\n            return;\n        }\n        navigator.mediaDevices.getUserMedia({\n            video: {\n                facingMode: \"environment\"\n            },\n            audio: false\n        }).then((stream)=>{\n            const videoElement = document.getElementById(\"camera-video\");\n            videoElement.srcObject = stream;\n            videoElement.play();\n        }).catch((error)=>{\n            console.error(\"Error accessing camera:\", error);\n        });\n    };\n    const captureImage = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (!videoElement) {\n            console.error(\"Video element not found\");\n            return;\n        }\n        const canvas = document.createElement(\"canvas\");\n        canvas.width = videoElement.videoWidth;\n        canvas.height = videoElement.videoHeight;\n        const context = canvas.getContext(\"2d\");\n        if (!context) {\n            console.error(\"Failed to get canvas context\");\n            return;\n        }\n        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);\n        const imageData = canvas.toDataURL(\"image/png\");\n        // Stop the camera stream after capturing the image\n        if (videoElement.srcObject) {\n            const stream = videoElement.srcObject;\n            const tracks = stream.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoElement.srcObject = null;\n        }\n        if (imageData) {\n            // setImage(imageData)\n            // setDisplayImage(true)\n            setAllImages((prev)=>[\n                    ...prev,\n                    {\n                        name: clientID + \"-capture-\" + Date.now(),\n                        imageData: imageData\n                    }\n                ]);\n            setImage(null);\n            setDisplayImage(false);\n            handleOpenCameraDialog(true);\n        }\n    };\n    const turnOffCamera = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (videoElement && videoElement.srcObject) {\n            const stream = videoElement.srcObject;\n            const tracks = stream.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoElement.srcObject = null;\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _localStorage_getItem;\n        setClientID(+((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0));\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (openCameraDialog) return;\n        turnOffCamera();\n    }, [\n        openCameraDialog\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (allFiles.length > 0) {\n            allFiles.forEach((file)=>{\n                if (file.title) {\n                    getFile({\n                        name: file.title,\n                        fieldName: inputId\n                    });\n                    setUploadFileNames((prev)=>{\n                        if (Array.isArray(prev)) {\n                            return [\n                                ...prev,\n                                file.title\n                            ];\n                        }\n                        return [\n                            file.title\n                        ];\n                    });\n                }\n            });\n        }\n    }, [\n        allFiles\n    ]);\n    const [createSectionMemberImage] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.CREATE_SECTION_MEMBER_IMAGE, {\n        onCompleted: (response)=>{\n            const data = response.createSectionMemberImage;\n            setFile();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating comment\", error);\n        }\n    });\n    async function uploadFile(file) {\n        // Upload file to Cloudflare\n        var fileName;\n        if (file.imageData) {\n            fileName = file.name || clientID + \"-capture-\" + Date.now();\n        }\n        if (!fileName) {\n            fileName = clientID + \"-capture-\" + Date.now();\n        }\n        createSectionMemberImage({\n            variables: {\n                input: {\n                    name: fileName,\n                    fieldName: inputId,\n                    imageType: \"FieldImage\",\n                    [sectionData.sectionName]: sectionData.sectionName === \"logBookEntryID\" ? logentryID : sectionData.id\n                }\n            }\n        });\n        if (file.imageData) {\n            s3Client.putObject({\n                Bucket: \"captures\",\n                Key: fileName,\n                Body: file.imageData\n            }, (err, data)=>{\n                if (err) {\n                    console.error(err);\n                } else {\n                    setFile();\n                }\n            });\n        }\n    }\n    const switchCamera = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (!videoElement) {\n            console.error(\"Video element not found\");\n            return;\n        }\n        const currentDeviceId = videoElement.srcObject ? videoElement.srcObject.getVideoTracks()[0].getSettings().deviceId : null;\n        const nextDevice = devices.find((device)=>device.kind === \"videoinput\" && device.deviceId !== currentDeviceId);\n        if (nextDevice) {\n            navigator.mediaDevices.getUserMedia({\n                video: {\n                    deviceId: nextDevice.deviceId\n                },\n                audio: false\n            }).then((stream)=>{\n                videoElement.srcObject = stream;\n                videoElement.play();\n            }).catch((error)=>{\n                console.error(\"Error switching camera:\", error);\n            });\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No other camera found to switch.\");\n        }\n    };\n    const handleUploadFile = ()=>{\n        if (allImages.length === 0) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please capture or upload an image first.\");\n            return;\n        }\n        allImages.forEach((img)=>{\n            uploadFile(img);\n        });\n        setAllImages([]);\n        setAllFiles([]);\n        setImage(null);\n        setDisplayImage(false);\n        setOpenCameraDialog(false);\n        turnOffCamera();\n        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Images uploaded successfully.\");\n    };\n    const [deleteSectionMemberImage] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.DELETE_SECTION_MEMBER_IMAGE, {\n        onCompleted: (response)=>{\n            const data = response.deleteCaptureImage;\n            if (data) {\n                setFile();\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image deleted successfully.\");\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error deleting image\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete image.\");\n        }\n    });\n    const handleDeleteImage = (img)=>{\n        setAllImages((prev)=>prev.filter((image)=>image.name !== img.name));\n        if (img.imageData) {\n            s3Client.deleteObject({\n                Bucket: \"captures\",\n                Key: img.name || \"\"\n            }, (err, data)=>{\n                if (err) {\n                    console.error(\"Error deleting image:\", err);\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image deleted successfully.\");\n                }\n            });\n            if (!img.id) {\n                return;\n            }\n            deleteSectionMemberImage({\n                variables: {\n                    ids: [\n                        +img.id\n                    ]\n                }\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: buttonType === \"icon\" ? \"ghost\" : \"outline\",\n                iconOnly: buttonType === \"icon\",\n                size: buttonType === \"icon\" ? \"icon\" : \"default\",\n                title: \"Add comment\",\n                className: buttonType === \"icon\" ? \"group\" : \"\",\n                iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: buttonType === \"icon\" ? (0,_utils_cn__WEBPACK_IMPORTED_MODULE_5__.cn)(file && file.length > 0 ? \"text-curious-blue-400 group-hover:text-curious-blue-400/50\" : \"text-outer-space-400 group-hover:text-outer-space-600\", \"will-change-transform will-change-width will-change-padding transform-gpu\", \"group-hover:transition-colors group-hover:ease-out group-hover:duration-300\") : \"\",\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 21\n                }, void 0),\n                onClick: ()=>handleOpenCameraDialog(false),\n                children: buttonType === \"button\" && (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__.getResponsiveLabel)(bp.phablet, \"Capture / Upload\", \"Capture / Upload Image\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                lineNumber: 460,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AlertDialogNew, {\n                openDialog: openCameraDialog,\n                setOpenDialog: setOpenCameraDialog,\n                size: \"lg\",\n                title: fileUpload ? \"Files\" : \"Camera\",\n                handleCreate: ()=>{\n                    // if (image) {\n                    handleUploadFile();\n                // setOpenCameraDialog(false)\n                // } else {\n                // toast.error('Please capture an image first.')\n                // }\n                },\n                handleCancel: ()=>{\n                    setOpenCameraDialog(false);\n                    setImage(null);\n                    setDisplayImage(false);\n                    setAllImages([]);\n                    turnOffCamera();\n                    setAllFiles([]);\n                    setFile();\n                },\n                actionText: \"Save\",\n                cancelText: \"Close\",\n                loading: false,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            allImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap mb-4\",\n                                children: allImages.filter((img)=>!(img === null || img === void 0 ? void 0 : img.id)).map((img, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1/4 p-1 rounded-md relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: img.imageData,\n                                                alt: \"Captured \".concat(index),\n                                                className: \"object-cover\",\n                                                onClick: ()=>{\n                                                    setImage(img.imageData);\n                                                    setDisplayImage(true);\n                                                    turnOffCamera();\n                                                }\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"destructive\",\n                                                size: \"icon\",\n                                                className: \"absolute top-1 right-1 p-0 size-5\",\n                                                onClick: ()=>{\n                                                    handleDeleteImage(img);\n                                                },\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 37\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 25\n                            }, this),\n                            fileUpload ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_upload_cf__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                files: allFiles,\n                                setFiles: setAllFiles,\n                                accept: \"image/*\",\n                                bucketName: \"captures\",\n                                multipleUpload: true,\n                                prefix: logentryID + \"-\",\n                                displayFiles: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 549,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                        id: \"camera-video\",\n                                        style: {\n                                            display: displayImage ? \"none\" : \"block\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: image,\n                                        alt: \"Captured\",\n                                        style: {\n                                            display: displayImage ? \"block\" : \"none\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                        lineNumber: 515,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-4 gap-2\",\n                        children: [\n                            !displayImage && !fileUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: captureImage,\n                                className: \"mt-2 w-full\",\n                                children: \"Capture\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 25\n                            }, this),\n                            displayImage && !fileUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                            devices.length < 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    switchCamera();\n                                },\n                                variant: \"outline\",\n                                className: \"mt-2 w-full\",\n                                children: \"Switch Camera\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 25\n                            }, this),\n                            fileUpload ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    setFileUpload(false);\n                                    handleOpenCameraDialog();\n                                },\n                                className: \"mt-2 w-full\",\n                                children: responsiveLabel(\"Capture\", \"Capture Image\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    turnOffCamera();\n                                    setFileUpload(true);\n                                },\n                                className: \"mt-2 w-full\",\n                                children: \"'Upload Image'\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                lineNumber: 490,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(UploadCloudFlareCaptures, \"hI3sxdLlederrl1l7Hbiq61gEeA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints,\n        _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__.useResponsiveLabel,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation\n    ];\n});\n_c = UploadCloudFlareCaptures;\nconst getCloudFlareImagesFile = (file)=>{\n    return new Promise((resolve, reject)=>{\n        if (!file || !file.name) {\n            console.error(\"No file name provided\");\n            reject(\"No file name provided\");\n            return;\n        }\n        s3Client.getObject({\n            Bucket: \"captures\",\n            Key: file.name\n        }, async (err, data)=>{\n            if (err) {\n                console.error(err);\n                reject(err);\n            } else {\n                if (!file || !file.name) {\n                    console.error(\"No file name provided\");\n                    reject(\"No file name provided\");\n                    return;\n                }\n                const fileType = file.name.split(\".\").pop() || \"\";\n                const blob = new Blob([\n                    new Uint8Array(data === null || data === void 0 ? void 0 : data.Body)\n                ]);\n                const url = URL.createObjectURL(blob);\n                if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {\n                    const base64String = Buffer.from(data === null || data === void 0 ? void 0 : data.Body).toString(\"base64\");\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    var base64Image = \"data:image/\".concat(fileType, \";base64,\").concat(base64String);\n                    if (!textContent.startsWith(\"�PNG\")) {\n                        base64Image = textContent;\n                    }\n                    resolve(base64Image);\n                } else {\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    resolve(textContent);\n                }\n            }\n        });\n    });\n};\nconst handleDeleteCFCaptureImage = (img, allImages)=>{\n    return new Promise(async (resolve, reject)=>{\n        if (!img || !img.name) {\n            console.error(\"No image name provided\");\n            reject(\"No image name provided\");\n            return;\n        }\n        s3Client.deleteObject({\n            Bucket: \"captures\",\n            Key: img.name\n        }, (err, data)=>{\n            if (err) {\n                console.error(\"Error deleting image:\", err);\n                reject(err);\n            } else {\n                resolve(allImages.filter((image)=>image.name !== img.name));\n            }\n        });\n    });\n};\nvar _c;\n$RefreshReg$(_c, \"UploadCloudFlareCaptures\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/components/upload-images.tsx\n"));

/***/ })

});