"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/maintenance/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/components/upload-images.tsx":
/*!*********************************************************!*\
  !*** ./src/app/ui/logbook/components/upload-images.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UploadCloudFlareCaptures; },\n/* harmony export */   getCloudFlareImagesFile: function() { return /* binding */ getCloudFlareImagesFile; },\n/* harmony export */   handleDeleteCFCaptureImage: function() { return /* binding */ handleDeleteCFCaptureImage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! aws-sdk */ \"(app-pages-browser)/./node_modules/.pnpm/aws-sdk@2.1692.0/node_modules/aws-sdk/lib/browser.js\");\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(aws_sdk__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Camera_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Camera!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../../utils/cn */ \"(app-pages-browser)/./utils/cn.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _upload_cf__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./upload-cf */ \"(app-pages-browser)/./src/app/ui/logbook/components/upload-cf.tsx\");\n/* harmony import */ var _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../../../utils/responsiveLabel */ \"(app-pages-browser)/./utils/responsiveLabel.ts\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/.pnpm/buffer@4.9.2/node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ default,getCloudFlareImagesFile,handleDeleteCFCaptureImage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ACCOUNT_ID = \"ddde1c1cd1aa25641691808dcbafdeb7\";\nconst ACCESS_KEY_ID = \"06c3e13a539f24e6fdf7075bf381bf5e\";\nconst SECRET_ACCESS_KEY = \"0bc23db8559504fb300b54def562d007e4a373fb940a7d07617ce906c553bbe8\";\nconst s3Client = new (aws_sdk__WEBPACK_IMPORTED_MODULE_2___default().S3)({\n    endpoint: \"https://\".concat(ACCOUNT_ID, \".r2.cloudflarestorage.com\"),\n    accessKeyId: ACCESS_KEY_ID,\n    secretAccessKey: SECRET_ACCESS_KEY,\n    signatureVersion: \"v4\",\n    region: \"auto\"\n});\nfunction UploadCloudFlareCaptures(param) {\n    let { file = false, setFile, inputId, buttonType = \"icon\", sectionData = {\n        id: 0,\n        sectionName: \"logBookEntryID\"\n    } } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const [openCameraDialog, setOpenCameraDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [image, setImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayImage, setDisplayImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clientID, setClientID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [devices, setDevices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fileUpload, setFileUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadFileNames, setUploadFileNames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allImages, setAllImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allFiles, setAllFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    const responsiveLabel = (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__.useResponsiveLabel)(\"phablet\");\n    const getFile = (file)=>{\n        if (!file || !file.name) {\n            console.error(\"No file name provided\");\n            return;\n        }\n        s3Client.getObject({\n            Bucket: \"captures\",\n            Key: file.name\n        }, async (err, data)=>{\n            if (err) {\n                console.error(err);\n                if (file.id) {\n                    deleteSectionMemberImage({\n                        variables: {\n                            ids: [\n                                +file.id\n                            ]\n                        }\n                    });\n                }\n            } else {\n                if (!file || !file.name) {\n                    console.error(\"No file name provided\");\n                    return;\n                }\n                const fileType = file.name.split(\".\").pop() || \"\";\n                const blob = new Blob([\n                    new Uint8Array(data === null || data === void 0 ? void 0 : data.Body)\n                ]);\n                const url = URL.createObjectURL(blob);\n                if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {\n                    // setImage(url)\n                    // setDisplayImage(true)\n                    const base64String = Buffer.from(data === null || data === void 0 ? void 0 : data.Body).toString(\"base64\");\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    var base64Image = \"data:image/\".concat(fileType, \";base64,\").concat(base64String);\n                    if (!textContent.startsWith(\"�PNG\")) {\n                        base64Image = textContent;\n                    }\n                    // setImage(base64Image)\n                    if (allImages.find((img)=>img.name === file.name) === undefined) {\n                        setAllImages((prev)=>[\n                                ...prev,\n                                {\n                                    ...file,\n                                    imageData: base64Image\n                                }\n                            ]);\n                    } else {\n                        setAllImages((prev)=>prev.map((img)=>img.name === file.name ? {\n                                    ...img,\n                                    imageData: base64Image\n                                } : img));\n                    }\n                } else {\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    // setImage(textContent)\n                    if (allImages.find((img)=>img.name === file.name) === undefined) {\n                        setAllImages((prev)=>[\n                                ...prev,\n                                {\n                                    ...file,\n                                    imageData: textContent\n                                }\n                            ]);\n                    } else {\n                        setAllImages((prev)=>prev.map((img)=>img.name === file.name ? {\n                                    ...img,\n                                    imageData: textContent\n                                } : img));\n                    }\n                // setDisplayImage(true)\n                }\n            }\n        });\n    };\n    // Handle opening the camera dialog\n    const handleOpenCameraDialog = async function() {\n        let bypass = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        setOpenCameraDialog(true);\n        setUploadFileNames(false);\n        setFileUpload(false);\n        setImage(null);\n        setDisplayImage(false);\n        if (file && file.length > 0 && !bypass) {\n            file.forEach((f)=>{\n                getFile(f);\n            });\n        // return\n        }\n        const devices = await navigator.mediaDevices.enumerateDevices();\n        const hasEnvironmentCamera = devices.some((device)=>device.kind === \"videoinput\");\n        if (hasEnvironmentCamera) {\n            setDevices(devices.filter((device)=>device.kind === \"videoinput\"));\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No camera found. Please connect a camera.\");\n            return;\n        }\n        navigator.mediaDevices.getUserMedia({\n            video: {\n                facingMode: \"environment\"\n            },\n            audio: false\n        }).then((stream)=>{\n            const videoElement = document.getElementById(\"camera-video\");\n            videoElement.srcObject = stream;\n            videoElement.play();\n        }).catch((error)=>{\n            console.error(\"Error accessing camera:\", error);\n        });\n    };\n    const captureImage = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (!videoElement) {\n            console.error(\"Video element not found\");\n            return;\n        }\n        const canvas = document.createElement(\"canvas\");\n        canvas.width = videoElement.videoWidth;\n        canvas.height = videoElement.videoHeight;\n        const context = canvas.getContext(\"2d\");\n        if (!context) {\n            console.error(\"Failed to get canvas context\");\n            return;\n        }\n        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);\n        const imageData = canvas.toDataURL(\"image/png\");\n        // Stop the camera stream after capturing the image\n        if (videoElement.srcObject) {\n            const stream = videoElement.srcObject;\n            const tracks = stream.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoElement.srcObject = null;\n        }\n        if (imageData) {\n            // setImage(imageData)\n            // setDisplayImage(true)\n            setAllImages((prev)=>[\n                    ...prev,\n                    {\n                        name: clientID + \"-capture-\" + Date.now(),\n                        imageData: imageData\n                    }\n                ]);\n            setImage(null);\n            setDisplayImage(false);\n            handleOpenCameraDialog(true);\n        }\n    };\n    const turnOffCamera = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (videoElement && videoElement.srcObject) {\n            const stream = videoElement.srcObject;\n            const tracks = stream.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoElement.srcObject = null;\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _localStorage_getItem;\n        setClientID(+((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0));\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (openCameraDialog) return;\n        turnOffCamera();\n    }, [\n        openCameraDialog\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (allFiles.length > 0) {\n            allFiles.forEach((file)=>{\n                if (file.title) {\n                    getFile({\n                        name: file.title,\n                        fieldName: inputId\n                    });\n                    setUploadFileNames((prev)=>{\n                        if (Array.isArray(prev)) {\n                            return [\n                                ...prev,\n                                file.title\n                            ];\n                        }\n                        return [\n                            file.title\n                        ];\n                    });\n                }\n            });\n        }\n    }, [\n        allFiles\n    ]);\n    const [createSectionMemberImage] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.CREATE_SECTION_MEMBER_IMAGE, {\n        onCompleted: (response)=>{\n            const data = response.createSectionMemberImage;\n            setFile();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating comment\", error);\n        }\n    });\n    async function uploadFile(file) {\n        // Upload file to Cloudflare\n        var fileName;\n        if (file.imageData) {\n            fileName = file.name || clientID + \"-capture-\" + Date.now();\n        }\n        if (!fileName) {\n            fileName = clientID + \"-capture-\" + Date.now();\n        }\n        createSectionMemberImage({\n            variables: {\n                input: {\n                    name: fileName,\n                    fieldName: inputId,\n                    imageType: \"FieldImage\",\n                    [sectionData.sectionName]: sectionData.sectionName === \"logBookEntryID\" ? logentryID : sectionData.id\n                }\n            }\n        });\n        if (file.imageData) {\n            s3Client.putObject({\n                Bucket: \"captures\",\n                Key: fileName,\n                Body: file.imageData\n            }, (err, data)=>{\n                if (err) {\n                    console.error(err);\n                } else {\n                    setFile();\n                }\n            });\n        }\n    }\n    const switchCamera = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (!videoElement) {\n            console.error(\"Video element not found\");\n            return;\n        }\n        const currentDeviceId = videoElement.srcObject ? videoElement.srcObject.getVideoTracks()[0].getSettings().deviceId : null;\n        const nextDevice = devices.find((device)=>device.kind === \"videoinput\" && device.deviceId !== currentDeviceId);\n        if (nextDevice) {\n            navigator.mediaDevices.getUserMedia({\n                video: {\n                    deviceId: nextDevice.deviceId\n                },\n                audio: false\n            }).then((stream)=>{\n                videoElement.srcObject = stream;\n                videoElement.play();\n            }).catch((error)=>{\n                console.error(\"Error switching camera:\", error);\n            });\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No other camera found to switch.\");\n        }\n    };\n    const handleUploadFile = ()=>{\n        if (allImages.length === 0) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please capture or upload an image first.\");\n            return;\n        }\n        allImages.forEach((img)=>{\n            uploadFile(img);\n        });\n        setAllImages([]);\n        setAllFiles([]);\n        setImage(null);\n        setDisplayImage(false);\n        setOpenCameraDialog(false);\n        turnOffCamera();\n        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Images uploaded successfully.\");\n    };\n    const [deleteSectionMemberImage] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.DELETE_SECTION_MEMBER_IMAGE, {\n        onCompleted: (response)=>{\n            const data = response.deleteCaptureImage;\n            if (data) {\n                setFile();\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image deleted successfully.\");\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error deleting image\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete image.\");\n        }\n    });\n    const handleDeleteImage = (img)=>{\n        setAllImages((prev)=>prev.filter((image)=>image.name !== img.name));\n        if (img.imageData) {\n            s3Client.deleteObject({\n                Bucket: \"captures\",\n                Key: img.name || \"\"\n            }, (err, data)=>{\n                if (err) {\n                    console.error(\"Error deleting image:\", err);\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image deleted successfully.\");\n                }\n            });\n            if (!img.id) {\n                return;\n            }\n            deleteSectionMemberImage({\n                variables: {\n                    ids: [\n                        +img.id\n                    ]\n                }\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: buttonType === \"icon\" ? \"ghost\" : \"outline\",\n                iconOnly: buttonType === \"icon\",\n                size: buttonType === \"icon\" ? \"icon\" : \"default\",\n                title: \"Add comment\",\n                className: buttonType === \"icon\" ? \"group\" : \"\",\n                iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: buttonType === \"icon\" ? (0,_utils_cn__WEBPACK_IMPORTED_MODULE_5__.cn)(file && file.length > 0 ? \"text-curious-blue-400 group-hover:text-curious-blue-400/50\" : \"text-outer-space-400 group-hover:text-outer-space-600\", \"will-change-transform will-change-width will-change-padding transform-gpu\", \"group-hover:transition-colors group-hover:ease-out group-hover:duration-300\") : \"\",\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                    lineNumber: 470,\n                    columnNumber: 21\n                }, void 0),\n                onClick: ()=>handleOpenCameraDialog(false),\n                children: buttonType === \"button\" && (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__.getResponsiveLabel)(bp.phablet, \"Capture / Upload\", \"Capture / Upload Image\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                lineNumber: 463,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AlertDialogNew, {\n                openDialog: openCameraDialog,\n                setOpenDialog: setOpenCameraDialog,\n                size: \"lg\",\n                title: fileUpload ? \"Files\" : \"Camera\",\n                handleCreate: ()=>{\n                    // if (image) {\n                    handleUploadFile();\n                // setOpenCameraDialog(false)\n                // } else {\n                // toast.error('Please capture an image first.')\n                // }\n                },\n                handleCancel: ()=>{\n                    setOpenCameraDialog(false);\n                    setImage(null);\n                    setDisplayImage(false);\n                    setAllImages([]);\n                    turnOffCamera();\n                    setAllFiles([]);\n                    setFile();\n                },\n                actionText: \"Save\",\n                cancelText: \"Close\",\n                loading: false,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            allImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap mb-4\",\n                                children: allImages.filter((img)=>!(img === null || img === void 0 ? void 0 : img.id)).map((img, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1/4 p-1 rounded-md relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: img.imageData,\n                                                alt: \"Captured \".concat(index),\n                                                className: \"object-cover\",\n                                                onClick: ()=>{\n                                                    setImage(img.imageData);\n                                                    setDisplayImage(true);\n                                                    turnOffCamera();\n                                                }\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"destructive\",\n                                                size: \"icon\",\n                                                className: \"absolute top-1 right-1 p-0 size-5\",\n                                                onClick: ()=>{\n                                                    handleDeleteImage(img);\n                                                },\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 37\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 25\n                            }, this),\n                            fileUpload ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_upload_cf__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                files: allFiles,\n                                setFiles: setAllFiles,\n                                accept: \"image/*\",\n                                bucketName: \"captures\",\n                                multipleUpload: true,\n                                prefix: logentryID + \"-\",\n                                displayFiles: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                        id: \"camera-video\",\n                                        style: {\n                                            display: displayImage ? \"none\" : \"block\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: image,\n                                        alt: \"Captured\",\n                                        style: {\n                                            display: displayImage ? \"block\" : \"none\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-4 gap-2\",\n                        children: [\n                            !displayImage && !fileUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: captureImage,\n                                className: \"mt-2 w-full\",\n                                children: \"Capture\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 25\n                            }, this),\n                            displayImage && !fileUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                            devices.length < 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    switchCamera();\n                                },\n                                variant: \"outline\",\n                                className: \"mt-2 w-full\",\n                                children: responsiveLabel(\"Switch Cam\", \"Switch Camera\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 608,\n                                columnNumber: 25\n                            }, this),\n                            fileUpload ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    setFileUpload(false);\n                                    handleOpenCameraDialog();\n                                },\n                                className: \"mt-2 w-full\",\n                                children: responsiveLabel(\"Capture\", \"Capture Image\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    turnOffCamera();\n                                    setFileUpload(true);\n                                },\n                                className: \"mt-2 w-full\",\n                                children: responsiveLabel(\"Upload\", \"Upload Image\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 627,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                        lineNumber: 578,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                lineNumber: 493,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(UploadCloudFlareCaptures, \"hI3sxdLlederrl1l7Hbiq61gEeA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints,\n        _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__.useResponsiveLabel,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation\n    ];\n});\n_c = UploadCloudFlareCaptures;\nconst getCloudFlareImagesFile = (file)=>{\n    return new Promise((resolve, reject)=>{\n        if (!file || !file.name) {\n            console.error(\"No file name provided\");\n            reject(\"No file name provided\");\n            return;\n        }\n        s3Client.getObject({\n            Bucket: \"captures\",\n            Key: file.name\n        }, async (err, data)=>{\n            if (err) {\n                console.error(err);\n                reject(err);\n            } else {\n                if (!file || !file.name) {\n                    console.error(\"No file name provided\");\n                    reject(\"No file name provided\");\n                    return;\n                }\n                const fileType = file.name.split(\".\").pop() || \"\";\n                const blob = new Blob([\n                    new Uint8Array(data === null || data === void 0 ? void 0 : data.Body)\n                ]);\n                const url = URL.createObjectURL(blob);\n                if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {\n                    const base64String = Buffer.from(data === null || data === void 0 ? void 0 : data.Body).toString(\"base64\");\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    var base64Image = \"data:image/\".concat(fileType, \";base64,\").concat(base64String);\n                    if (!textContent.startsWith(\"�PNG\")) {\n                        base64Image = textContent;\n                    }\n                    resolve(base64Image);\n                } else {\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    resolve(textContent);\n                }\n            }\n        });\n    });\n};\nconst handleDeleteCFCaptureImage = (img, allImages)=>{\n    return new Promise(async (resolve, reject)=>{\n        if (!img || !img.name) {\n            console.error(\"No image name provided\");\n            reject(\"No image name provided\");\n            return;\n        }\n        s3Client.deleteObject({\n            Bucket: \"captures\",\n            Key: img.name\n        }, (err, data)=>{\n            if (err) {\n                console.error(\"Error deleting image:\", err);\n                reject(err);\n            } else {\n                resolve(allImages.filter((image)=>image.name !== img.name));\n            }\n        });\n    });\n};\nvar _c;\n$RefreshReg$(_c, \"UploadCloudFlareCaptures\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/components/upload-images.tsx\n"));

/***/ })

});