"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/maintenance/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/components/upload-images.tsx":
/*!*********************************************************!*\
  !*** ./src/app/ui/logbook/components/upload-images.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UploadCloudFlareCaptures; },\n/* harmony export */   getCloudFlareImagesFile: function() { return /* binding */ getCloudFlareImagesFile; },\n/* harmony export */   handleDeleteCFCaptureImage: function() { return /* binding */ handleDeleteCFCaptureImage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! aws-sdk */ \"(app-pages-browser)/./node_modules/.pnpm/aws-sdk@2.1692.0/node_modules/aws-sdk/lib/browser.js\");\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(aws_sdk__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Camera_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Camera!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../../utils/cn */ \"(app-pages-browser)/./utils/cn.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _upload_cf__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./upload-cf */ \"(app-pages-browser)/./src/app/ui/logbook/components/upload-cf.tsx\");\n/* harmony import */ var _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../../../utils/responsiveLabel */ \"(app-pages-browser)/./utils/responsiveLabel.ts\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/.pnpm/buffer@4.9.2/node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ default,getCloudFlareImagesFile,handleDeleteCFCaptureImage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ACCOUNT_ID = \"ddde1c1cd1aa25641691808dcbafdeb7\";\nconst ACCESS_KEY_ID = \"06c3e13a539f24e6fdf7075bf381bf5e\";\nconst SECRET_ACCESS_KEY = \"0bc23db8559504fb300b54def562d007e4a373fb940a7d07617ce906c553bbe8\";\nconst s3Client = new (aws_sdk__WEBPACK_IMPORTED_MODULE_2___default().S3)({\n    endpoint: \"https://\".concat(ACCOUNT_ID, \".r2.cloudflarestorage.com\"),\n    accessKeyId: ACCESS_KEY_ID,\n    secretAccessKey: SECRET_ACCESS_KEY,\n    signatureVersion: \"v4\",\n    region: \"auto\"\n});\nfunction UploadCloudFlareCaptures(param) {\n    let { file = false, setFile, inputId, buttonType = \"icon\", sectionData = {\n        id: 0,\n        sectionName: \"logBookEntryID\"\n    } } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const [openCameraDialog, setOpenCameraDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [image, setImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayImage, setDisplayImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clientID, setClientID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [devices, setDevices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fileUpload, setFileUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadFileNames, setUploadFileNames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allImages, setAllImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allFiles, setAllFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    const getFile = (file)=>{\n        if (!file || !file.name) {\n            console.error(\"No file name provided\");\n            return;\n        }\n        s3Client.getObject({\n            Bucket: \"captures\",\n            Key: file.name\n        }, async (err, data)=>{\n            if (err) {\n                console.error(err);\n                if (file.id) {\n                    deleteSectionMemberImage({\n                        variables: {\n                            ids: [\n                                +file.id\n                            ]\n                        }\n                    });\n                }\n            } else {\n                if (!file || !file.name) {\n                    console.error(\"No file name provided\");\n                    return;\n                }\n                const fileType = file.name.split(\".\").pop() || \"\";\n                const blob = new Blob([\n                    new Uint8Array(data === null || data === void 0 ? void 0 : data.Body)\n                ]);\n                const url = URL.createObjectURL(blob);\n                if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {\n                    // setImage(url)\n                    // setDisplayImage(true)\n                    const base64String = Buffer.from(data === null || data === void 0 ? void 0 : data.Body).toString(\"base64\");\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    var base64Image = \"data:image/\".concat(fileType, \";base64,\").concat(base64String);\n                    if (!textContent.startsWith(\"�PNG\")) {\n                        base64Image = textContent;\n                    }\n                    // setImage(base64Image)\n                    if (allImages.find((img)=>img.name === file.name) === undefined) {\n                        setAllImages((prev)=>[\n                                ...prev,\n                                {\n                                    ...file,\n                                    imageData: base64Image\n                                }\n                            ]);\n                    } else {\n                        setAllImages((prev)=>prev.map((img)=>img.name === file.name ? {\n                                    ...img,\n                                    imageData: base64Image\n                                } : img));\n                    }\n                } else {\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    // setImage(textContent)\n                    if (allImages.find((img)=>img.name === file.name) === undefined) {\n                        setAllImages((prev)=>[\n                                ...prev,\n                                {\n                                    ...file,\n                                    imageData: textContent\n                                }\n                            ]);\n                    } else {\n                        setAllImages((prev)=>prev.map((img)=>img.name === file.name ? {\n                                    ...img,\n                                    imageData: textContent\n                                } : img));\n                    }\n                // setDisplayImage(true)\n                }\n            }\n        });\n    };\n    // Handle opening the camera dialog\n    const handleOpenCameraDialog = async function() {\n        let bypass = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        setOpenCameraDialog(true);\n        setUploadFileNames(false);\n        setFileUpload(false);\n        setImage(null);\n        setDisplayImage(false);\n        if (file && file.length > 0 && !bypass) {\n            file.forEach((f)=>{\n                getFile(f);\n            });\n        // return\n        }\n        const devices = await navigator.mediaDevices.enumerateDevices();\n        const hasEnvironmentCamera = devices.some((device)=>device.kind === \"videoinput\");\n        if (hasEnvironmentCamera) {\n            setDevices(devices.filter((device)=>device.kind === \"videoinput\"));\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No camera found. Please connect a camera.\");\n            return;\n        }\n        navigator.mediaDevices.getUserMedia({\n            video: {\n                facingMode: \"environment\"\n            },\n            audio: false\n        }).then((stream)=>{\n            const videoElement = document.getElementById(\"camera-video\");\n            videoElement.srcObject = stream;\n            videoElement.play();\n        }).catch((error)=>{\n            console.error(\"Error accessing camera:\", error);\n        });\n    };\n    const captureImage = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (!videoElement) {\n            console.error(\"Video element not found\");\n            return;\n        }\n        const canvas = document.createElement(\"canvas\");\n        canvas.width = videoElement.videoWidth;\n        canvas.height = videoElement.videoHeight;\n        const context = canvas.getContext(\"2d\");\n        if (!context) {\n            console.error(\"Failed to get canvas context\");\n            return;\n        }\n        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);\n        const imageData = canvas.toDataURL(\"image/png\");\n        // Stop the camera stream after capturing the image\n        if (videoElement.srcObject) {\n            const stream = videoElement.srcObject;\n            const tracks = stream.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoElement.srcObject = null;\n        }\n        if (imageData) {\n            // setImage(imageData)\n            // setDisplayImage(true)\n            setAllImages((prev)=>[\n                    ...prev,\n                    {\n                        name: clientID + \"-capture-\" + Date.now(),\n                        imageData: imageData\n                    }\n                ]);\n            setImage(null);\n            setDisplayImage(false);\n            handleOpenCameraDialog(true);\n        }\n    };\n    const turnOffCamera = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (videoElement && videoElement.srcObject) {\n            const stream = videoElement.srcObject;\n            const tracks = stream.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoElement.srcObject = null;\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _localStorage_getItem;\n        setClientID(+((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0));\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (openCameraDialog) return;\n        turnOffCamera();\n    }, [\n        openCameraDialog\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (allFiles.length > 0) {\n            allFiles.forEach((file)=>{\n                if (file.title) {\n                    getFile({\n                        name: file.title,\n                        fieldName: inputId\n                    });\n                    setUploadFileNames((prev)=>{\n                        if (Array.isArray(prev)) {\n                            return [\n                                ...prev,\n                                file.title\n                            ];\n                        }\n                        return [\n                            file.title\n                        ];\n                    });\n                }\n            });\n        }\n    }, [\n        allFiles\n    ]);\n    const [createSectionMemberImage] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.CREATE_SECTION_MEMBER_IMAGE, {\n        onCompleted: (response)=>{\n            const data = response.createSectionMemberImage;\n            setFile();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating comment\", error);\n        }\n    });\n    async function uploadFile(file) {\n        // Upload file to Cloudflare\n        var fileName;\n        if (file.imageData) {\n            fileName = file.name || clientID + \"-capture-\" + Date.now();\n        }\n        if (!fileName) {\n            fileName = clientID + \"-capture-\" + Date.now();\n        }\n        createSectionMemberImage({\n            variables: {\n                input: {\n                    name: fileName,\n                    fieldName: inputId,\n                    imageType: \"FieldImage\",\n                    [sectionData.sectionName]: sectionData.sectionName === \"logBookEntryID\" ? logentryID : sectionData.id\n                }\n            }\n        });\n        if (file.imageData) {\n            s3Client.putObject({\n                Bucket: \"captures\",\n                Key: fileName,\n                Body: file.imageData\n            }, (err, data)=>{\n                if (err) {\n                    console.error(err);\n                } else {\n                    setFile();\n                }\n            });\n        }\n    }\n    const switchCamera = ()=>{\n        const videoElement = document.getElementById(\"camera-video\");\n        if (!videoElement) {\n            console.error(\"Video element not found\");\n            return;\n        }\n        const currentDeviceId = videoElement.srcObject ? videoElement.srcObject.getVideoTracks()[0].getSettings().deviceId : null;\n        const nextDevice = devices.find((device)=>device.kind === \"videoinput\" && device.deviceId !== currentDeviceId);\n        if (nextDevice) {\n            navigator.mediaDevices.getUserMedia({\n                video: {\n                    deviceId: nextDevice.deviceId\n                },\n                audio: false\n            }).then((stream)=>{\n                videoElement.srcObject = stream;\n                videoElement.play();\n            }).catch((error)=>{\n                console.error(\"Error switching camera:\", error);\n            });\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No other camera found to switch.\");\n        }\n    };\n    const handleUploadFile = ()=>{\n        if (allImages.length === 0) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please capture or upload an image first.\");\n            return;\n        }\n        allImages.forEach((img)=>{\n            uploadFile(img);\n        });\n        setAllImages([]);\n        setAllFiles([]);\n        setImage(null);\n        setDisplayImage(false);\n        setOpenCameraDialog(false);\n        turnOffCamera();\n        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Images uploaded successfully.\");\n    };\n    const [deleteSectionMemberImage] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.DELETE_SECTION_MEMBER_IMAGE, {\n        onCompleted: (response)=>{\n            const data = response.deleteCaptureImage;\n            if (data) {\n                setFile();\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image deleted successfully.\");\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error deleting image\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete image.\");\n        }\n    });\n    const handleDeleteImage = (img)=>{\n        setAllImages((prev)=>prev.filter((image)=>image.name !== img.name));\n        if (img.imageData) {\n            s3Client.deleteObject({\n                Bucket: \"captures\",\n                Key: img.name || \"\"\n            }, (err, data)=>{\n                if (err) {\n                    console.error(\"Error deleting image:\", err);\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.dismiss();\n                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image deleted successfully.\");\n                }\n            });\n            if (!img.id) {\n                return;\n            }\n            deleteSectionMemberImage({\n                variables: {\n                    ids: [\n                        +img.id\n                    ]\n                }\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: buttonType === \"icon\" ? \"ghost\" : \"outline\",\n                iconOnly: buttonType === \"icon\",\n                size: buttonType === \"icon\" ? \"icon\" : \"default\",\n                title: \"Add comment\",\n                className: buttonType === \"icon\" ? \"group\" : \"\",\n                iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: buttonType === \"icon\" ? (0,_utils_cn__WEBPACK_IMPORTED_MODULE_5__.cn)(file && file.length > 0 ? \"text-curious-blue-400 group-hover:text-curious-blue-400/50\" : \"text-outer-space-400 group-hover:text-outer-space-600\", \"will-change-transform will-change-width will-change-padding transform-gpu\", \"group-hover:transition-colors group-hover:ease-out group-hover:duration-300\") : \"\",\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 21\n                }, void 0),\n                onClick: ()=>handleOpenCameraDialog(false),\n                children: buttonType === \"button\" && (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_9__.getResponsiveLabel)(bp.phablet, \"Capture / Upload\", \"Capture / Upload Image\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                lineNumber: 458,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AlertDialogNew, {\n                openDialog: openCameraDialog,\n                setOpenDialog: setOpenCameraDialog,\n                size: \"lg\",\n                title: fileUpload ? \"Files\" : \"Camera\",\n                handleCreate: ()=>{\n                    // if (image) {\n                    handleUploadFile();\n                // setOpenCameraDialog(false)\n                // } else {\n                // toast.error('Please capture an image first.')\n                // }\n                },\n                handleCancel: ()=>{\n                    setOpenCameraDialog(false);\n                    setImage(null);\n                    setDisplayImage(false);\n                    setAllImages([]);\n                    turnOffCamera();\n                    setAllFiles([]);\n                    setFile();\n                },\n                actionText: \"Save\",\n                cancelText: \"Close\",\n                loading: false,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            allImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap mb-4\",\n                                children: allImages.filter((img)=>!(img === null || img === void 0 ? void 0 : img.id)).map((img, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1/4 p-1 rounded-md relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: img.imageData,\n                                                alt: \"Captured \".concat(index),\n                                                className: \"object-cover\",\n                                                onClick: ()=>{\n                                                    setImage(img.imageData);\n                                                    setDisplayImage(true);\n                                                    turnOffCamera();\n                                                }\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"destructive\",\n                                                size: \"icon\",\n                                                className: \"absolute top-1 right-1 p-0 size-5\",\n                                                onClick: ()=>{\n                                                    handleDeleteImage(img);\n                                                },\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 37\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 25\n                            }, this),\n                            fileUpload ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_upload_cf__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                files: allFiles,\n                                setFiles: setAllFiles,\n                                accept: \"image/*\",\n                                bucketName: \"captures\",\n                                multipleUpload: true,\n                                prefix: logentryID + \"-\",\n                                displayFiles: false\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 547,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                        id: \"camera-video\",\n                                        style: {\n                                            display: displayImage ? \"none\" : \"block\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: image,\n                                        alt: \"Captured\",\n                                        style: {\n                                            display: displayImage ? \"block\" : \"none\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                        lineNumber: 513,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-4 gap-2\",\n                        children: [\n                            !displayImage && !fileUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: captureImage,\n                                className: \"mt-2 w-full\",\n                                children: \"Capture\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 575,\n                                columnNumber: 25\n                            }, this),\n                            displayImage && !fileUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                            devices.length < 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    switchCamera();\n                                },\n                                variant: \"outline\",\n                                className: \"mt-2 w-full\",\n                                children: \"Switch Camera\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 25\n                            }, this),\n                            fileUpload ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    setFileUpload(false);\n                                    handleOpenCameraDialog();\n                                },\n                                className: \"mt-2 w-full\",\n                                children: \"Capture Image\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 613,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    turnOffCamera();\n                                    setFileUpload(true);\n                                },\n                                className: \"mt-2 w-full\",\n                                children: \"Upload Image\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                        lineNumber: 573,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\upload-images.tsx\",\n                lineNumber: 488,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(UploadCloudFlareCaptures, \"tO+FzjAwrNpn7Czrp7cxVnPeGmE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation\n    ];\n});\n_c = UploadCloudFlareCaptures;\nconst getCloudFlareImagesFile = (file)=>{\n    return new Promise((resolve, reject)=>{\n        if (!file || !file.name) {\n            console.error(\"No file name provided\");\n            reject(\"No file name provided\");\n            return;\n        }\n        s3Client.getObject({\n            Bucket: \"captures\",\n            Key: file.name\n        }, async (err, data)=>{\n            if (err) {\n                console.error(err);\n                reject(err);\n            } else {\n                if (!file || !file.name) {\n                    console.error(\"No file name provided\");\n                    reject(\"No file name provided\");\n                    return;\n                }\n                const fileType = file.name.split(\".\").pop() || \"\";\n                const blob = new Blob([\n                    new Uint8Array(data === null || data === void 0 ? void 0 : data.Body)\n                ]);\n                const url = URL.createObjectURL(blob);\n                if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {\n                    const base64String = Buffer.from(data === null || data === void 0 ? void 0 : data.Body).toString(\"base64\");\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    var base64Image = \"data:image/\".concat(fileType, \";base64,\").concat(base64String);\n                    if (!textContent.startsWith(\"�PNG\")) {\n                        base64Image = textContent;\n                    }\n                    resolve(base64Image);\n                } else {\n                    const textContent = new TextDecoder().decode(data === null || data === void 0 ? void 0 : data.Body);\n                    resolve(textContent);\n                }\n            }\n        });\n    });\n};\nconst handleDeleteCFCaptureImage = (img, allImages)=>{\n    return new Promise(async (resolve, reject)=>{\n        if (!img || !img.name) {\n            console.error(\"No image name provided\");\n            reject(\"No image name provided\");\n            return;\n        }\n        s3Client.deleteObject({\n            Bucket: \"captures\",\n            Key: img.name\n        }, (err, data)=>{\n            if (err) {\n                console.error(\"Error deleting image:\", err);\n                reject(err);\n            } else {\n                resolve(allImages.filter((image)=>image.name !== img.name));\n            }\n        });\n    });\n};\nvar _c;\n$RefreshReg$(_c, \"UploadCloudFlareCaptures\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/components/upload-images.tsx\n"));

/***/ })

});